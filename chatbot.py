"""
Multimodal Agentic RAG Chatbot with RAG Prioritization
Main chatbot implementation with intelligent document handling and RAG-first approach
"""

import os
import uuid
import time
import logging
from typing import Dict, Optional, Any
from dotenv import load_dotenv

# Import core components
from agent_router import AgentRouter
from session_manager import SessionManager
from reasoning_engine import ChainOfThoughtReasoning
from openai import OpenAI

# Load environment variables
load_dotenv()

class MultimodalAgenticRAGChatbot:
    """
    Main chatbot class implementing RAG-first document analysis with intelligent fallback
    """
    
    def __init__(self):
        """Initialize chatbot with all core components"""
        self.agent_router = AgentRouter()
        self.session_manager = SessionManager()
        self.reasoning_engine = ChainOfThoughtReasoning(
            openai_client=OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        )
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def process_query(self, query: str, session_id: str = None, uploaded_file=None) -> str:
        """
        Main query processing pipeline with RAG-first document handling
        
        Args:
            query: User's question or request
            session_id: Session identifier for context continuity
            uploaded_file: Optional uploaded document for analysis
            
        Returns:
            Generated response based on RAG prioritization logic
        """
        try:
            # Create or get session
            if not session_id:
                session_id = str(uuid.uuid4())
            
            session = self.session_manager.get_or_create_session(session_id)
            
            # Process uploaded document if provided
            uploaded_document = None
            if uploaded_file:
                self.logger.info(f"Processing uploaded document: {uploaded_file.name}")
                doc_analysis = self.process_document(uploaded_file, session_id)
                uploaded_document = doc_analysis
                session['documents'].append(doc_analysis)
            
            # Route query with document context using RAG prioritization
            self.logger.info(f"Processing query: {query[:100]}...")
            response = self.agent_router.route_query(query, session, uploaded_document)

            # Apply reasoning for complex queries ONLY if RAG didn't provide a good response
            if self.is_complex_query(query) and self._should_apply_reasoning(response):
                self.logger.info("Applying chain-of-thought reasoning")
                response = self.reasoning_engine.apply_reasoning(query, session)
            
            # Update session context
            self.session_manager.update_context(session_id, query, response)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing query: {str(e)}")
            return self._handle_error(e)
    
    def process_document(self, uploaded_file, session_id: str) -> Dict[str, Any]:
        """
        Process uploaded document and prepare for RAG-prioritized analysis
        
        Args:
            uploaded_file: File object from Streamlit or Flask
            session_id: Session identifier
            
        Returns:
            Document analysis data with relevance assessment
        """
        try:
            # Save uploaded file temporarily
            temp_file_path = f"temp_{session_id}_{uploaded_file.name}"
            
            # Handle different file input types (Streamlit vs Flask)
            if hasattr(uploaded_file, 'getbuffer'):
                # Streamlit file uploader
                with open(temp_file_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())
            else:
                # Flask file upload
                uploaded_file.save(temp_file_path)
            
            # Analyze document using document analyzer
            doc_data = self.agent_router.doc_analyzer.analyze_document(temp_file_path)
            
            # Add file metadata for later reference
            doc_data['file_path'] = temp_file_path
            doc_data['original_name'] = uploaded_file.name
            doc_data['session_id'] = session_id
            doc_data['upload_time'] = time.time()
            
            self.logger.info(f"Document processed: {uploaded_file.name}, "
                           f"relevance: {doc_data.get('relevance', 'N/A')}")
            
            return doc_data
            
        except Exception as e:
            self.logger.error(f"Error processing document: {str(e)}")
            raise Exception(f"Unable to process document: {str(e)}")
    
    def is_complex_query(self, query: str) -> bool:
        """
        Determine if query requires chain of thought reasoning
        
        Args:
            query: User's question
            
        Returns:
            True if query is complex and needs reasoning
        """
        complex_indicators = [
            'compare', 'analyze', 'calculate', 'plan', 'strategy',
            'pros and cons', 'best approach', 'step by step',
            'explain how', 'what are the implications', 'should i',
            'recommend', 'advise', 'multiple options'
        ]
        
        query_lower = query.lower()
        return any(indicator in query_lower for indicator in complex_indicators)

    def _should_apply_reasoning(self, response: str) -> bool:
        """
        Determine if chain-of-thought reasoning should be applied based on the response quality

        Args:
            response: The response from the agent router

        Returns:
            True if reasoning should be applied (response is insufficient)
        """
        if not response or len(response.strip()) < 100:
            return True

        # Don't override if we have a good RAG response with source attribution
        if "📚 **Source**: Knowledge Base" in response:
            return False

        # Don't override if we have a good LLM response with source attribution
        if "🤖 **Source**: AI Analysis" in response and len(response.strip()) > 200:
            return False

        # Don't override if we have web search results
        if "🌐 **Source**: Web Search" in response:
            return False

        # Apply reasoning if response seems generic or insufficient
        generic_indicators = [
            "I don't have specific information",
            "I'm not sure about",
            "Please provide more context",
            "Could you clarify"
        ]

        return any(indicator in response for indicator in generic_indicators)

    def _handle_error(self, error: Exception) -> str:
        """
        Handle errors gracefully with user-friendly messages
        
        Args:
            error: Exception that occurred
            
        Returns:
            User-friendly error message
        """
        error_messages = {
            'OpenAI': "I'm experiencing technical difficulties with my AI processing. Please try again in a moment.",
            'Qdrant': "My knowledge base is temporarily unavailable. Please retry your query.",
            'FileProcessing': "I couldn't process the uploaded document. Please check the file format and try again.",
            'Network': "I'm having trouble connecting to external services. Please check your internet connection.",
        }
        
        error_type = type(error).__name__
        for key, message in error_messages.items():
            if key.lower() in error_type.lower():
                return message
        
        return "I encountered an unexpected error. Please try rephrasing your question or try again later."
    
    def get_session_info(self, session_id: str) -> Optional[Dict]:
        """
        Get session information for debugging or analytics
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session data or None if not found
        """
        return self.session_manager.get_session(session_id)
    
    def cleanup_session(self, session_id: str) -> bool:
        """
        Clean up session and associated temporary files
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if cleanup successful
        """
        try:
            session = self.session_manager.get_session(session_id)
            if session:
                # Clean up temporary document files
                for doc in session.get('documents', []):
                    file_path = doc.get('file_path')
                    if file_path and os.path.exists(file_path):
                        os.remove(file_path)
                        self.logger.info(f"Cleaned up temporary file: {file_path}")
                
                # Remove session
                self.session_manager.remove_session(session_id)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Error cleaning up session {session_id}: {str(e)}")
            return False


if __name__ == "__main__":
    # Simple test
    chatbot = MultimodalAgenticRAGChatbot()
    print("Chatbot initialized successfully!")
    
    # Test basic query
    response = chatbot.process_query("What are the basic tax deductions available?")
    print(f"Response: {response}")
