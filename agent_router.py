"""
Agent Router with RAG Prioritization Logic
Intelligent query routing system that prioritizes RAG for document-related queries
with fallback to normal LLM when documents are irrelevant
"""

import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Import core components
from rag_system import RAGSystem
from document_analyzer import DocumentA<PERSON>yzer
from web_search_tool import WebSearchTool

load_dotenv()

class AgentRouter:
    """
    Intelligent agent router implementing RAG-first prioritization for document queries
    """
    
    def __init__(self):
        """Initialize router with all processing tools"""
        self.rag_system = RAGSystem()
        self.web_search = WebSearchTool()
        self.doc_analyzer = DocumentAnalyzer(self.rag_system)
        self.logger = logging.getLogger(__name__)
    
    def route_query(self, query: str, context: Dict, uploaded_document: Optional[Dict] = None) -> str:
        """
        Route query to appropriate tool based on classification with RAG prioritization
        
        Args:
            query: User's question
            context: Session context including conversation history
            uploaded_document: Optional document data with relevance assessment
            
        Returns:
            Generated response using appropriate processing approach
        """
        try:
            # Special handling for document-related queries with RAG prioritization
            if uploaded_document and self.is_document_query(query):
                self.logger.info("Processing document-related query with RAG prioritization")
                return self.handle_document_query(query, context, uploaded_document)
            
            # Standard query routing for non-document queries
            query_type = self.classify_query(query)
            self.logger.info(f"Query classified as: {query_type}")
            
            if query_type == "rag_domain":
                rag_result = self.rag_system.query(query, context)
                return self._format_response_with_metadata(rag_result)
            elif query_type == "web_search":
                web_result = self.web_search.search(query)
                return self._format_web_search_response(web_result, query)
            elif query_type == "document_analysis":
                doc_result = self.doc_analyzer.analyze_query_without_document(query, context)
                return self._format_document_analysis_response(doc_result, query)
            else:
                general_result = self.handle_general_query(query, context)
                return general_result
                
        except Exception as e:
            self.logger.error(f"Error in query routing: {str(e)}")
            return f"I encountered an error processing your query. Please try again."

    def _format_response_with_metadata(self, rag_result: Dict) -> str:
        """Format RAG response with detailed source information"""
        if isinstance(rag_result, dict) and 'response' in rag_result:
            response = rag_result['response']
            metadata = rag_result.get('metadata', {})

            # Add detailed source information
            source_info = self._create_detailed_source_info(metadata)
            return f"{response}\n\n{source_info}"
        else:
            # Fallback for old format
            return str(rag_result)

    def _format_web_search_response(self, web_result: str, query: str) -> str:
        """Format web search response with source attribution"""
        source_info = "🌐 **Source**: Web Search (Real-time information)"
        return f"{web_result}\n\n{source_info}"

    def _format_document_analysis_response(self, doc_result: str, query: str) -> str:
        """Format document analysis response with source attribution"""
        source_info = "📄 **Source**: Document Analysis (LLM-based)"
        return f"{doc_result}\n\n{source_info}"

    def _create_detailed_source_info(self, metadata: Dict) -> str:
        """Create detailed source information from metadata"""
        response_type = metadata.get('response_type', 'UNKNOWN')
        sources_used = metadata.get('sources_used', [])
        qdrant_available = metadata.get('qdrant_available', False)
        fallback_used = metadata.get('fallback_used', False)

        if response_type == 'RAG' and qdrant_available:
            results_by_source = metadata.get('results_by_source', {})
            total_results = metadata.get('total_results', 0)
            top_scores = metadata.get('top_scores', [])

            source_details = []
            for source in sources_used:
                count = results_by_source.get(source, 0)
                source_details.append(f"{source} ({count} results)")

            max_score = max(top_scores) if top_scores else 0

            return f"""📚 **Source**: Knowledge Base (RAG)
🔍 **Collections Used**: {', '.join(source_details)}
📊 **Total Results**: {total_results}
⭐ **Best Match Score**: {max_score:.3f}
🎯 **Vector Search**: Active"""

        elif response_type == 'BUILTIN_KNOWLEDGE':
            return "📚 **Source**: Built-in CA Knowledge (Fallback mode - Qdrant unavailable)"

        elif response_type == 'LLM_ONLY':
            if fallback_used:
                return "🤖 **Source**: LLM Knowledge Only (Fallback mode - Qdrant unavailable)"
            else:
                return "🤖 **Source**: LLM Knowledge Only"
        else:
            return f"❓ **Source**: {response_type}"
    
    def is_document_query(self, query: str) -> bool:
        """
        Enhanced document query detection for RAG prioritization
        
        Args:
            query: User's question
            
        Returns:
            True if query is asking about the uploaded document
        """
        document_indicators = [
            'this document', 'this file', 'this pdf', 'this invoice', 'this bill',
            'uploaded document', 'attached file', 'in this', 'from this',
            'analyze this', 'what is this', 'explain this', 'summarize this',
            'calculate from this', 'extract from this', 'details in this',
            'what does this show', 'according to this', 'based on this document',
            'in the document', 'document shows', 'file contains'
        ]
        
        query_lower = query.lower()
        return any(indicator in query_lower for indicator in document_indicators)
    
    def handle_document_query(self, query: str, context: Dict, uploaded_document: Dict) -> str:
        """
        Handle document-specific queries with RAG prioritization strategy
        
        Args:
            query: User's question about the document
            context: Session context
            uploaded_document: Document data with content and metadata
            
        Returns:
            Response using appropriate RAG prioritization tier
        """
        try:
            # Get document relevance score (should be calculated during document processing)
            relevance_score = uploaded_document.get('relevance', 0.0)
            document_content = uploaded_document.get('content', '')
            
            self.logger.info(f"Document relevance score: {relevance_score}")
            
            # Apply RAG prioritization strategy based on relevance
            if relevance_score > 0.7:
                # Tier 1: RAG Priority - High relevance to CA domain
                self.logger.info("Using RAG Priority approach (Tier 1)")
                return self.doc_analyzer.answer_with_rag_priority(query, document_content, context)
                
            elif relevance_score > 0.3:
                # Tier 2: Hybrid Approach - Medium relevance
                self.logger.info("Using Hybrid approach (Tier 2)")
                return self.doc_analyzer.answer_with_hybrid_approach(query, document_content, context)
                
            else:
                # Tier 3: LLM-Only Fallback - Low relevance
                self.logger.info("Using LLM-only fallback (Tier 3)")
                return self.doc_analyzer.answer_with_llm_only(query, document_content, context)
                
        except Exception as e:
            self.logger.error(f"Error handling document query: {str(e)}")
            return "I encountered an error analyzing the document. Please try again."
    
    def classify_query(self, query: str, has_document: bool = False, document_relevance: float = 0.0) -> str:
        """
        Classify query using keyword matching and context analysis

        Args:
            query: User's question
            has_document: Whether a document is uploaded
            document_relevance: Document relevance score (0-1)

        Returns:
            Query classification type
        """
        # Comprehensive domain-specific keywords for RAG routing
        rag_keywords = [
            # Tax-related terms
            'tax', 'taxes', 'taxation', 'income tax', 'corporate tax', 'capital gains',
            'gst', 'vat', 'service tax', 'tds', 'tcs', 'advance tax', 'self assessment',
            'itr', 'return', 'filing', 'refund', 'assessment', 'notice', 'penalty',
            'deduction', 'deductions', 'exemption', 'exemptions', 'rebate', 'relief',
            'section 80c', '80d', '80g', 'hra', 'medical', 'education loan',

            # Legal and compliance
            'legal', 'law', 'compliance', 'regulation', 'act', 'rules',
            'company law', 'corporate law', 'labour law', 'contract',
            'agreement', 'license', 'registration', 'incorporation',
            'mca', 'roc', 'din', 'pan', 'tan', 'cin', 'llp',

            # Business and accounting
            'accounting', 'accounts', 'financial', 'finance', 'audit', 'auditing',
            'balance sheet', 'profit loss', 'p&l', 'cash flow', 'budget',
            'bookkeeping', 'journal', 'ledger', 'trial balance', 'depreciation',
            'inventory', 'assets', 'liabilities', 'equity', 'revenue', 'expenses',

            # Business structures
            'business', 'company', 'firm', 'partnership', 'proprietorship',
            'corporation', 'pvt ltd', 'private limited', 'public limited',
            'sole proprietorship', 'partnership firm', 'llp', 'opc',

            # Professional services
            'ca', 'chartered accountant', 'cs', 'company secretary',
            'cma', 'cost accountant', 'advisory', 'consultation', 'professional',

            # Government and schemes
            'government', 'scheme', 'subsidy', 'grant', 'loan', 'mudra',
            'startup', 'msme', 'ssi', 'export', 'import', 'fema',

            # Banking and finance
            'bank', 'banking', 'loan', 'credit', 'investment', 'mutual fund',
            'insurance', 'policy', 'premium', 'claim', 'npa', 'rbi',

            # Common business queries
            'how to', 'procedure', 'process', 'steps', 'requirements',
            'documents required', 'eligibility', 'benefits', 'advantages',
            'disadvantages', 'comparison', 'difference between'
        ]

        # Web search keywords (more specific to avoid conflicts)
        web_keywords = [
            'latest news', 'breaking news', 'current news', 'today news',
            'recent updates', 'trending now', 'what happened today',
            'who is currently', 'latest announcement', 'recent changes',
            'new policy announced', 'budget 2024', 'union budget'
        ]

        query_lower = query.lower()

        # Check for web search indicators (more specific patterns)
        web_search_patterns = [
            'latest', 'current', 'now', 'today', 'recent', 'new', 'updated',
            'trending', 'news', 'who is', 'what happened', 'when did', 'breaking'
        ]

        # Only classify as web search if it's clearly asking for current/recent information
        if any(pattern in query_lower for pattern in web_search_patterns) and \
           any(keyword in query_lower for keyword in web_keywords):
            return "web_search"

        # Check for RAG domain keywords (much more comprehensive now)
        elif any(keyword in query_lower for keyword in rag_keywords):
            return "rag_domain"

        # Check for document analysis without specific document
        elif any(word in query_lower for word in ['analyze', 'review', 'check', 'examine']):
            return "document_analysis"

        # Default to RAG domain for most queries since we're a CA assistant
        # This ensures more queries get the benefit of RAG knowledge
        else:
            return "rag_domain"  # Changed from "general" to "rag_domain"
    
    def handle_general_query(self, query: str, context: Dict) -> str:
        """
        Handle general queries - now primarily uses RAG since most queries should benefit from knowledge base

        Args:
            query: User's question
            context: Session context

        Returns:
            RAG-enhanced response or fallback general response
        """
        try:
            # Always try RAG first since we've made classification more inclusive
            self.logger.info("Attempting RAG query for general query")
            rag_result = self.rag_system.query(query, context)

            # Handle dict format (new format)
            if isinstance(rag_result, dict):
                rag_response = rag_result.get('response', '')
                metadata = rag_result.get('metadata', {})

                # Check if RAG provided a meaningful response
                if len(rag_response.strip()) > 50 and not metadata.get('fallback_used', False):
                    self.logger.info("RAG provided good response, using it")
                    return self._format_response_with_metadata(rag_result)
                else:
                    self.logger.info("RAG response insufficient or fallback used, generating general response")
                    # Still use the RAG response if it exists, but add context
                    if len(rag_response.strip()) > 20:
                        return self._format_response_with_metadata(rag_result)
                    else:
                        # Complete fallback
                        general_response = self._generate_general_response(query, context)
                        return general_response
            else:
                # Handle old string format (shouldn't happen with current implementation)
                if len(str(rag_result).strip()) > 50:
                    self.logger.info("Using RAG string response")
                    return str(rag_result)
                else:
                    self.logger.info("RAG string response insufficient")
                    general_response = self._generate_general_response(query, context)
                    return general_response

        except Exception as e:
            self.logger.error(f"Error handling general query: {str(e)}")
            # Fallback to general response on error
            general_response = self._generate_general_response(query, context)
            return general_response
    
    def _generate_general_response(self, query: str, context: Dict) -> str:
        """
        Generate general response for queries when RAG doesn't provide sufficient results

        Args:
            query: User's question
            context: Session context

        Returns:
            General helpful response with CA context
        """
        # Try to provide a helpful response even for general queries
        general_response = f"""I'll do my best to help with your question: "{query}"

As your CA assistant, I have access to comprehensive knowledge about:

• **Tax Matters**: Income tax, GST, TDS/TCS, deductions, exemptions, filing procedures
• **Legal & Compliance**: Company law, business registration, regulatory requirements
• **Business Structure**: Partnership, proprietorship, private limited companies, LLP
• **Financial Planning**: Accounting, auditing, financial analysis, investment advice
• **Government Schemes**: Startup benefits, MSME schemes, subsidies and grants

While I may not have found specific information in my knowledge base for your exact query, I can still provide general guidance based on my professional expertise.

If you could provide more specific details about your situation or rephrase your question with more context about the tax, legal, or business aspects you're interested in, I can give you more targeted assistance."""

        # Add source attribution for general response
        source_info = "🤖 **Source**: AI Assistant (General CA Knowledge - Knowledge base search yielded limited results)"
        return f"{general_response}\n\n{source_info}"


if __name__ == "__main__":
    # Simple test
    router = AgentRouter()
    print("Agent Router initialized successfully!")
    
    # Test query classification
    test_queries = [
        "What are the tax deductions for AY 2024-25?",
        "Who is the richest person now?",
        "Analyze this document"
    ]
    
    for query in test_queries:
        classification = router.classify_query(query)
        print(f"Query: {query} -> Classification: {classification}")
