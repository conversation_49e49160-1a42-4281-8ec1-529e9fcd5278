"""
Test script for Source Attribution Feature
Demonstrates how the system shows sources of information
"""

import os
import sys
from dotenv import load_dotenv

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def test_source_attribution():
    """Test source attribution in different scenarios"""
    print("🧪 Testing Source Attribution Feature")
    print("=" * 50)
    
    if not os.getenv('OPENAI_API_KEY'):
        print("⚠️  OPENAI_API_KEY not found. Please configure it to test source attribution.")
        return False
    
    try:
        from chatbot import MultimodalAgenticRAGChatbot
        
        chatbot = MultimodalAgenticRAGChatbot()
        
        # Test scenarios
        test_cases = [
            {
                "query": "What are the basic tax deductions available?",
                "expected_source": "Knowledge Base",
                "description": "RAG query - should show knowledge base source"
            },
            {
                "query": "Who is the current finance minister?",
                "expected_source": "Web Search",
                "description": "Current info query - should show web search source"
            },
            {
                "query": "Hello, how are you?",
                "expected_source": "AI Assistant",
                "description": "General query - should show AI assistant source"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test {i}: {test_case['description']}")
            print(f"Query: {test_case['query']}")
            
            try:
                response = chatbot.process_query(test_case['query'])
                
                # Check if response contains source attribution
                if "**Source**:" in response:
                    print("✅ Source attribution found")
                    
                    # Extract source line
                    lines = response.split('\n')
                    source_line = [line for line in lines if "**Source**:" in line]
                    if source_line:
                        print(f"📚 {source_line[0]}")
                    
                    # Check if expected source type is mentioned
                    if test_case['expected_source'].lower() in response.lower():
                        print(f"✅ Expected source type '{test_case['expected_source']}' found")
                    else:
                        print(f"⚠️  Expected source type '{test_case['expected_source']}' not found")
                else:
                    print("❌ No source attribution found in response")
                
                print(f"📝 Response preview: {response[:150]}...")
                
            except Exception as e:
                print(f"❌ Error testing query: {str(e)}")
        
        print("\n" + "=" * 50)
        print("🎯 Source Attribution Test Summary:")
        print("✅ System now shows source of information for each response")
        print("📚 RAG responses show knowledge base collections used")
        print("🤖 LLM responses show AI analysis source")
        print("🌐 Web search responses show real-time information source")
        print("🔄 Hybrid responses show combined sources")
        print("📄 Document analysis shows document + analysis source")
        
        return True
        
    except Exception as e:
        print(f"❌ Source attribution test failed: {str(e)}")
        return False

def test_rag_collections():
    """Test that the system is configured for the correct collections"""
    print("\n🧪 Testing RAG Collection Configuration")
    print("=" * 50)
    
    try:
        from rag_system import RAGSystem
        
        rag_system = RAGSystem()
        
        if hasattr(rag_system, 'collection_names'):
            print(f"✅ Collections configured: {rag_system.collection_names}")
            
            expected_collections = ["TAX-RAG-1", "tax_documents"]
            if rag_system.collection_names == expected_collections:
                print("✅ Correct collections configured")
                return True
            else:
                print(f"⚠️  Expected {expected_collections}, got {rag_system.collection_names}")
                return False
        else:
            print("❌ No collections configured (fallback mode)")
            return False
            
    except Exception as e:
        print(f"❌ Collection test failed: {str(e)}")
        return False

def demonstrate_source_types():
    """Demonstrate different source types"""
    print("\n📖 Source Attribution Types:")
    print("=" * 50)
    
    source_types = [
        ("📚", "Knowledge Base (Collections: TAX-RAG-1, tax_documents)", "RAG responses from vector store"),
        ("🤖", "AI Analysis (General Knowledge)", "Direct LLM responses"),
        ("🌐", "Web Search (Real-time Information)", "Current information from web"),
        ("🔄", "Hybrid (Knowledge Base + AI Analysis + Document)", "Combined approach"),
        ("📄", "AI Analysis + Uploaded Document", "Document analysis"),
        ("ℹ️", "Web Search Fallback (Guidance to Official Sources)", "When web search unavailable")
    ]
    
    for icon, source, description in source_types:
        print(f"{icon} **Source**: {source}")
        print(f"   └─ {description}")
        print()

if __name__ == "__main__":
    print("🚀 Source Attribution & RAG Collection Test")
    print("=" * 60)
    
    # Test RAG collection configuration
    collections_ok = test_rag_collections()
    
    # Demonstrate source types
    demonstrate_source_types()
    
    # Test source attribution (requires API key)
    attribution_ok = test_source_attribution()
    
    print("\n" + "=" * 60)
    if collections_ok and attribution_ok:
        print("🎉 All tests passed! Source attribution is working correctly.")
        print("\n🎯 Key Features:")
        print("✅ Uses only TAX-RAG-1 and tax_documents collections")
        print("✅ Shows source of information for every response")
        print("✅ Users can understand where information comes from")
        print("✅ Transparent AI system with clear attribution")
    else:
        print("⚠️  Some tests had issues. Check the output above.")
        if not collections_ok:
            print("   - RAG collection configuration needs attention")
        if not attribution_ok:
            print("   - Source attribution feature needs attention")
