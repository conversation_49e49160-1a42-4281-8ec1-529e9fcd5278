"""
Comprehensive Vector Store Diagnostic Tool
Diagnoses and fixes RAG system retrieval issues
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams
except ImportError:
    print("Error: qdrant-client not installed. Please install it with: pip install qdrant-client")
    sys.exit(1)

from openai import OpenAI

class VectorStoreDiagnostic:
    """Comprehensive diagnostic tool for vector store issues"""
    
    def __init__(self):
        """Initialize diagnostic tool"""
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.qdrant_client = None
        self.collection_names = ["TAX-RAG-1", "tax_documents"]
        
        # Initialize Qdrant client
        try:
            self.qdrant_client = QdrantClient(
                url=os.getenv('QDRANT_URL'),
                api_key=os.getenv('QDRANT_API_KEY')
            )
            logger.info("✅ Qdrant client initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Qdrant client: {str(e)}")
            sys.exit(1)
    
    def diagnose_collections(self):
        """Comprehensive collection diagnosis"""
        print("🔍 COMPREHENSIVE VECTOR STORE DIAGNOSIS")
        print("=" * 70)
        
        results = {}
        
        for collection_name in self.collection_names:
            print(f"\n📋 Analyzing Collection: {collection_name}")
            print("-" * 50)
            
            collection_info = self.analyze_collection_structure(collection_name)
            results[collection_name] = collection_info
            
            # Test different search methods
            self.test_search_methods(collection_name, collection_info)
        
        return results
    
    def analyze_collection_structure(self, collection_name: str) -> Dict:
        """Analyze collection structure and configuration"""
        try:
            # Get collection info
            collection_info = self.qdrant_client.get_collection(collection_name)
            
            info = {
                'exists': True,
                'points_count': collection_info.points_count,
                'config': collection_info.config.dict() if hasattr(collection_info.config, 'dict') else str(collection_info.config),
                'vectors': {},
                'distance_metric': None
            }
            
            print(f"✅ Collection exists with {info['points_count']} points")
            
            # Analyze vector configuration
            if hasattr(collection_info.config, 'params'):
                params = collection_info.config.params
                if hasattr(params, 'vectors'):
                    vectors_config = params.vectors
                    
                    if isinstance(vectors_config, dict):
                        # Multiple named vectors
                        print(f"📊 Named vectors found: {list(vectors_config.keys())}")
                        for vector_name, vector_config in vectors_config.items():
                            info['vectors'][vector_name] = {
                                'size': vector_config.size if hasattr(vector_config, 'size') else 'unknown',
                                'distance': str(vector_config.distance) if hasattr(vector_config, 'distance') else 'unknown'
                            }
                            print(f"   - {vector_name}: size={info['vectors'][vector_name]['size']}, distance={info['vectors'][vector_name]['distance']}")
                    else:
                        # Single vector
                        info['vectors']['default'] = {
                            'size': vectors_config.size if hasattr(vectors_config, 'size') else 'unknown',
                            'distance': str(vectors_config.distance) if hasattr(vectors_config, 'distance') else 'unknown'
                        }
                        print(f"📊 Single vector: size={info['vectors']['default']['size']}, distance={info['vectors']['default']['distance']}")
            
            return info
            
        except Exception as e:
            logger.error(f"❌ Failed to analyze collection {collection_name}: {str(e)}")
            return {'exists': False, 'error': str(e)}
    
    def test_search_methods(self, collection_name: str, collection_info: Dict):
        """Test different search methods for the collection"""
        print(f"\n🔍 Testing Search Methods for {collection_name}")
        
        # Generate test embedding
        test_embedding = self.get_embedding("tax audit procedures compliance")
        if not test_embedding:
            print("❌ Failed to generate test embedding")
            return
        
        # Test different search approaches
        if collection_name == "TAX-RAG-1" and collection_info.get('vectors'):
            # Test named vector searches
            for vector_name in collection_info['vectors'].keys():
                self.test_named_vector_search(collection_name, vector_name, test_embedding)
        else:
            # Test default vector search
            self.test_default_vector_search(collection_name, test_embedding)
    
    def test_named_vector_search(self, collection_name: str, vector_name: str, embedding: List[float]):
        """Test search with named vector"""
        try:
            results = self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=(vector_name, embedding),
                limit=3,
                with_payload=True,
                with_vectors=False
            )
            
            print(f"✅ {vector_name} search: {len(results)} results")
            
            if results:
                for i, result in enumerate(results[:2]):  # Show first 2 results
                    print(f"   Result {i+1}: score={result.score:.4f}")
                    if result.payload:
                        # Show some payload info
                        payload_keys = list(result.payload.keys())[:3]  # First 3 keys
                        print(f"   Payload keys: {payload_keys}")
                        
                        # Show content preview if available
                        content_fields = ['text', 'content', 'chunk', 'document']
                        for field in content_fields:
                            if field in result.payload:
                                content = str(result.payload[field])[:100]
                                print(f"   Content preview: {content}...")
                                break
            
        except Exception as e:
            print(f"❌ {vector_name} search failed: {str(e)}")
    
    def test_default_vector_search(self, collection_name: str, embedding: List[float]):
        """Test search with default vector"""
        try:
            results = self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=embedding,
                limit=3,
                with_payload=True,
                with_vectors=False
            )
            
            print(f"✅ Default search: {len(results)} results")
            
            if results:
                for i, result in enumerate(results[:2]):  # Show first 2 results
                    print(f"   Result {i+1}: score={result.score:.4f}")
                    if result.payload:
                        # Show some payload info
                        payload_keys = list(result.payload.keys())[:3]  # First 3 keys
                        print(f"   Payload keys: {payload_keys}")
                        
                        # Show content preview if available
                        content_fields = ['text', 'content', 'chunk', 'document']
                        for field in content_fields:
                            if field in result.payload:
                                content = str(result.payload[field])[:100]
                                print(f"   Content preview: {content}...")
                                break
            
        except Exception as e:
            print(f"❌ Default search failed: {str(e)}")
    
    def get_embedding(self, text: str) -> List[float]:
        """Generate embedding for text"""
        try:
            response = self.openai_client.embeddings.create(
                input=text,
                model="text-embedding-ada-002"
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            return []
    
    def test_specific_queries(self):
        """Test with specific tax/audit queries"""
        print(f"\n🎯 TESTING SPECIFIC TAX/AUDIT QUERIES")
        print("=" * 70)
        
        test_queries = [
            "Video-based Customer Identification Process V-CIP auditor verification",
            "RBI INR-denominated bond overseas default auditor assessment",
            "GST compliance audit procedures",
            "Section 80C tax deductions verification",
            "audit procedures tax compliance",
            "customer identification process banking"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: {query}")
            print("-" * 40)
            
            embedding = self.get_embedding(query)
            if not embedding:
                print("❌ Failed to generate embedding")
                continue
            
            # Test both collections
            for collection_name in self.collection_names:
                self.search_collection_with_query(collection_name, query, embedding)
    
    def search_collection_with_query(self, collection_name: str, query: str, embedding: List[float]):
        """Search a specific collection with a query"""
        try:
            if collection_name == "TAX-RAG-1":
                # Try both vector types for TAX-RAG-1
                for vector_name in ["text-dense", "text-sparse-new"]:
                    try:
                        results = self.qdrant_client.search(
                            collection_name=collection_name,
                            query_vector=(vector_name, embedding),
                            limit=2,
                            with_payload=True,
                            score_threshold=0.1  # Lower threshold for testing
                        )
                        
                        if results:
                            print(f"✅ {collection_name} ({vector_name}): {len(results)} results")
                            best_result = results[0]
                            print(f"   Best score: {best_result.score:.4f}")
                            
                            # Show content if available
                            if best_result.payload:
                                content_fields = ['text', 'content', 'chunk', 'document']
                                for field in content_fields:
                                    if field in best_result.payload:
                                        content = str(best_result.payload[field])[:150]
                                        print(f"   Content: {content}...")
                                        break
                            break  # Success with this vector type
                        else:
                            print(f"⚠️  {collection_name} ({vector_name}): No results above threshold")
                            
                    except Exception as e:
                        print(f"❌ {collection_name} ({vector_name}) failed: {str(e)}")
            else:
                # tax_documents collection
                results = self.qdrant_client.search(
                    collection_name=collection_name,
                    query_vector=embedding,
                    limit=2,
                    with_payload=True,
                    score_threshold=0.1  # Lower threshold for testing
                )
                
                if results:
                    print(f"✅ {collection_name}: {len(results)} results")
                    best_result = results[0]
                    print(f"   Best score: {best_result.score:.4f}")
                    
                    # Show content if available
                    if best_result.payload:
                        content_fields = ['text', 'content', 'chunk', 'document']
                        for field in content_fields:
                            if field in best_result.payload:
                                content = str(best_result.payload[field])[:150]
                                print(f"   Content: {content}...")
                                break
                else:
                    print(f"⚠️  {collection_name}: No results above threshold")
                    
        except Exception as e:
            print(f"❌ {collection_name} search failed: {str(e)}")

def main():
    """Main diagnostic function"""
    print("🚀 VECTOR STORE DIAGNOSTIC TOOL")
    print("=" * 70)
    
    # Check environment variables
    required_vars = ['OPENAI_API_KEY', 'QDRANT_URL', 'QDRANT_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return
    
    diagnostic = VectorStoreDiagnostic()
    
    # Run comprehensive diagnosis
    collection_results = diagnostic.diagnose_collections()
    
    # Test specific queries
    diagnostic.test_specific_queries()
    
    print(f"\n📊 DIAGNOSIS SUMMARY")
    print("=" * 70)
    
    for collection_name, info in collection_results.items():
        if info.get('exists'):
            print(f"✅ {collection_name}: {info['points_count']} points, {len(info.get('vectors', {}))} vector types")
        else:
            print(f"❌ {collection_name}: {info.get('error', 'Unknown error')}")
    
    print(f"\n🔧 RECOMMENDED FIXES:")
    print("1. Ensure TAX-RAG-1 uses named vector specification (text-dense or text-sparse-new)")
    print("2. Lower similarity thresholds if no results are found")
    print("3. Verify embedding model consistency")
    print("4. Check if collections contain relevant tax/audit content")

if __name__ == "__main__":
    main()
