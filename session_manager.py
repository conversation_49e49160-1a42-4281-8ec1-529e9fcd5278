"""
Session Manager for Cha<PERSON><PERSON>
Handles session creation, context management, and cleanup
"""

import time
import logging
from typing import Dict, Optional, List, Any
from threading import Lock

class SessionManager:
    """
    Manages user sessions and conversation context
    """
    
    def __init__(self, max_session_age: int = 3600):
        """
        Initialize session manager
        
        Args:
            max_session_age: Maximum session age in seconds (default: 1 hour)
        """
        self.sessions: Dict[str, Dict] = {}
        self.max_session_age = max_session_age
        self.lock = Lock()  # Thread safety for concurrent access
        self.logger = logging.getLogger(__name__)
        
        # Start cleanup process
        self._last_cleanup = time.time()
        self._cleanup_interval = 300  # 5 minutes
    
    def create_session(self, session_id: str) -> Dict[str, Any]:
        """
        Create new session with empty context
        
        Args:
            session_id: Unique session identifier
            
        Returns:
            New session data
        """
        with self.lock:
            session = {
                'id': session_id,
                'context': [],
                'documents': [],
                'created_at': time.time(),
                'last_activity': time.time(),
                'query_count': 0,
                'metadata': {}
            }
            
            self.sessions[session_id] = session
            self.logger.info(f"Created new session: {session_id}")
            
            # Periodic cleanup
            self._periodic_cleanup()
            
            return session
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get existing session
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session data or None if not found
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if session:
                # Update last activity
                session['last_activity'] = time.time()
            return session
    
    def get_or_create_session(self, session_id: str) -> Dict[str, Any]:
        """
        Get existing session or create new one
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session data
        """
        session = self.get_session(session_id)
        if session is None:
            session = self.create_session(session_id)
        return session
    
    def update_context(self, session_id: str, query: str, response: str) -> bool:
        """
        Update session context with new query-response pair
        
        Args:
            session_id: Session identifier
            query: User's query
            response: Bot's response
            
        Returns:
            True if update successful
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                self.logger.warning(f"Session not found for context update: {session_id}")
                return False
            
            # Add new context entry
            context_entry = {
                'query': query,
                'response': response,
                'timestamp': time.time()
            }
            
            session['context'].append(context_entry)
            session['last_activity'] = time.time()
            session['query_count'] += 1
            
            # Keep only last 10 exchanges to manage memory
            if len(session['context']) > 10:
                session['context'] = session['context'][-10:]
            
            self.logger.debug(f"Updated context for session {session_id}")
            return True
    
    def add_document(self, session_id: str, document_data: Dict[str, Any]) -> bool:
        """
        Add document to session
        
        Args:
            session_id: Session identifier
            document_data: Document analysis data
            
        Returns:
            True if addition successful
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                self.logger.warning(f"Session not found for document addition: {session_id}")
                return False
            
            session['documents'].append(document_data)
            session['last_activity'] = time.time()
            
            # Keep only last 5 documents to manage memory
            if len(session['documents']) > 5:
                session['documents'] = session['documents'][-5:]
            
            self.logger.info(f"Added document to session {session_id}")
            return True
    
    def get_recent_context(self, session_id: str, count: int = 3) -> List[Dict[str, Any]]:
        """
        Get recent conversation context
        
        Args:
            session_id: Session identifier
            count: Number of recent exchanges to return
            
        Returns:
            List of recent context entries
        """
        session = self.get_session(session_id)
        if not session or not session.get('context'):
            return []
        
        return session['context'][-count:]
    
    def get_session_documents(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get documents associated with session
        
        Args:
            session_id: Session identifier
            
        Returns:
            List of document data
        """
        session = self.get_session(session_id)
        if not session:
            return []
        
        return session.get('documents', [])
    
    def remove_session(self, session_id: str) -> bool:
        """
        Remove session and cleanup resources
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if removal successful
        """
        with self.lock:
            if session_id in self.sessions:
                del self.sessions[session_id]
                self.logger.info(f"Removed session: {session_id}")
                return True
            return False
    
    def cleanup_expired_sessions(self) -> int:
        """
        Remove expired sessions
        
        Returns:
            Number of sessions cleaned up
        """
        current_time = time.time()
        expired_sessions = []
        
        with self.lock:
            for session_id, session in self.sessions.items():
                if current_time - session['last_activity'] > self.max_session_age:
                    expired_sessions.append(session_id)
            
            # Remove expired sessions
            for session_id in expired_sessions:
                del self.sessions[session_id]
        
        if expired_sessions:
            self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
        
        return len(expired_sessions)
    
    def _periodic_cleanup(self):
        """Perform periodic cleanup if needed"""
        current_time = time.time()
        if current_time - self._last_cleanup > self._cleanup_interval:
            self.cleanup_expired_sessions()
            self._last_cleanup = current_time
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        Get session statistics
        
        Returns:
            Dictionary with session statistics
        """
        with self.lock:
            total_sessions = len(self.sessions)
            total_queries = sum(session.get('query_count', 0) for session in self.sessions.values())
            total_documents = sum(len(session.get('documents', [])) for session in self.sessions.values())
            
            # Calculate average session age
            current_time = time.time()
            if total_sessions > 0:
                avg_age = sum(current_time - session['created_at'] for session in self.sessions.values()) / total_sessions
            else:
                avg_age = 0
            
            return {
                'total_sessions': total_sessions,
                'total_queries': total_queries,
                'total_documents': total_documents,
                'average_session_age_minutes': avg_age / 60,
                'max_session_age_hours': self.max_session_age / 3600
            }
    
    def update_session_metadata(self, session_id: str, metadata: Dict[str, Any]) -> bool:
        """
        Update session metadata
        
        Args:
            session_id: Session identifier
            metadata: Metadata to update
            
        Returns:
            True if update successful
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return False
            
            session['metadata'].update(metadata)
            session['last_activity'] = time.time()
            return True


if __name__ == "__main__":
    # Simple test
    session_manager = SessionManager()
    print("Session Manager initialized successfully!")
    
    # Test session creation
    test_session_id = "test_session_123"
    session = session_manager.create_session(test_session_id)
    print(f"Created session: {session['id']}")
    
    # Test context update
    session_manager.update_context(test_session_id, "Test query", "Test response")
    
    # Test stats
    stats = session_manager.get_session_stats()
    print(f"Session stats: {stats}")
