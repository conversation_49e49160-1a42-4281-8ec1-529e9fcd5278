"""
Test script to verify RAG system is working with both collections
"""

import os
import sys
from dotenv import load_dotenv

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def test_rag_collections():
    """Test RAG system with both collections"""
    print("🧪 Testing RAG System with Both Collections")
    print("=" * 60)
    
    if not all([os.getenv('OPENAI_API_KEY'), os.getenv('QDRANT_URL'), os.getenv('QDRANT_API_KEY')]):
        print("⚠️  Missing required environment variables (OPENAI_API_KEY, QDRANT_URL, QDRANT_API_KEY)")
        return False
    
    try:
        from rag_system import RAGSystem
        
        rag_system = RAGSystem()
        
        # Test collection connectivity
        print("📋 Testing Collection Connectivity...")
        test_results = rag_system.test_collections()
        
        if test_results:
            print("✅ Collection test results:")
            for test_name, result in test_results.items():
                status = "✅" if result else "❌"
                print(f"   {status} {test_name}")
        else:
            print("❌ Collection testing failed")
            return False
        
        # Test actual queries
        print("\n📋 Testing Actual Queries...")
        
        test_queries = [
            "What are the procedural safeguards that an auditor must verify regarding Video-based Customer Identification Process (V-CIP)?",
            "How should an auditor assess the exposure and subsequent reporting requirements if the RBI underwrites an INR-denominated bond overseas?",
            "What are the key audit procedures for GST compliance?",
            "How to verify tax deductions under Section 80C?"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Test Query {i}: {query[:80]}...")
            
            try:
                response = rag_system.query(query, {})
                
                if response and len(response.strip()) > 50:
                    print("✅ Query successful")
                    
                    # Check if both collections are mentioned in source
                    if "TAX-RAG-1" in response and "tax_documents" in response:
                        print("✅ Both collections used")
                    elif "TAX-RAG-1" in response:
                        print("⚠️  Only TAX-RAG-1 used")
                    elif "tax_documents" in response:
                        print("⚠️  Only tax_documents used")
                    else:
                        print("❌ No collection sources found")
                    
                    # Show source attribution
                    lines = response.split('\n')
                    source_lines = [line for line in lines if "**Source**:" in line]
                    if source_lines:
                        print(f"📚 {source_lines[0]}")
                    
                    print(f"📝 Response preview: {response[:150]}...")
                else:
                    print("❌ Query returned minimal response")
                    print(f"Response: {response}")
                
            except Exception as e:
                print(f"❌ Query failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG system test failed: {str(e)}")
        return False

def test_chatbot_integration():
    """Test full chatbot integration"""
    print("\n🧪 Testing Full Chatbot Integration")
    print("=" * 60)
    
    try:
        from chatbot import MultimodalAgenticRAGChatbot
        
        chatbot = MultimodalAgenticRAGChatbot()
        
        # Test the specific queries from the user's image
        user_queries = [
            "What procedural safeguards must an auditor verify regarding Video-based Customer Identification Process (V-CIP), and what does arise if such processes are poorly implemented?",
            "If the RBI underwrites an INR-denominated bond overseas and the bond defaults on it, how must the auditor assess the exposure and subsequent reporting requirements?"
        ]
        
        for i, query in enumerate(user_queries, 1):
            print(f"\n🔍 User Query {i}: {query[:80]}...")
            
            try:
                response = chatbot.process_query(query)
                
                if "knowledge base does not provide specific information" in response.lower():
                    print("❌ Knowledge base not finding information")
                    print("🔧 This suggests the vector search is not working properly")
                elif len(response.strip()) > 100:
                    print("✅ Detailed response generated")
                    
                    # Check source attribution
                    if "TAX-RAG-1" in response:
                        print("✅ TAX-RAG-1 collection used")
                    if "tax_documents" in response:
                        print("✅ tax_documents collection used")
                    
                    # Show source line
                    lines = response.split('\n')
                    source_lines = [line for line in lines if "**Source**:" in line]
                    if source_lines:
                        print(f"📚 {source_lines[0]}")
                else:
                    print("⚠️  Short response generated")
                
                print(f"📝 Response preview: {response[:200]}...")
                
            except Exception as e:
                print(f"❌ Chatbot query failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chatbot integration test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 RAG System Fix Verification")
    print("=" * 70)
    
    # Test RAG collections
    rag_ok = test_rag_collections()
    
    # Test chatbot integration
    chatbot_ok = test_chatbot_integration()
    
    print("\n" + "=" * 70)
    print("📊 Test Summary:")
    
    if rag_ok and chatbot_ok:
        print("🎉 All tests passed! RAG system is working correctly.")
        print("\n✅ Key Fixes Applied:")
        print("   - Fixed vector name specification for TAX-RAG-1 collection")
        print("   - Added fallback handling for different vector configurations")
        print("   - Improved error handling and logging")
        print("   - Both collections are now properly accessible")
        print("\n🎯 The chatbot should now:")
        print("   - Successfully search both TAX-RAG-1 and tax_documents")
        print("   - Show both collections in source attribution")
        print("   - Provide detailed answers from your knowledge base")
    else:
        print("❌ Some tests failed. Issues to address:")
        if not rag_ok:
            print("   - RAG system connectivity or configuration issues")
        if not chatbot_ok:
            print("   - Chatbot integration issues")
        
        print("\n🔧 Troubleshooting steps:")
        print("   1. Verify Qdrant credentials are correct")
        print("   2. Check that collections TAX-RAG-1 and tax_documents exist")
        print("   3. Verify vector configurations in collections")
        print("   4. Check network connectivity to Qdrant")

if __name__ == "__main__":
    main()
