"""
Setup script for Multimodal Agentic RAG Chatbot
Handles environment setup and configuration validation
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def check_environment_file():
    """Check if .env file exists and has required variables"""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists():
        if env_example.exists():
            print("⚠️  .env file not found. Please copy .env.example to .env and configure your API keys")
            print("   cp .env.example .env")
        else:
            print("❌ Neither .env nor .env.example file found")
        return False
    
    # Load environment variables
    load_dotenv()
    
    required_vars = ['OPENAI_API_KEY']
    optional_vars = ['QDRANT_URL', 'QDRANT_API_KEY', 'BRAVE_API_KEY']
    
    missing_required = []
    missing_optional = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_required.append(var)
        else:
            print(f"✅ {var}: Configured")
    
    for var in optional_vars:
        if not os.getenv(var):
            missing_optional.append(var)
        else:
            print(f"✅ {var}: Configured")
    
    if missing_required:
        print(f"❌ Missing required environment variables: {', '.join(missing_required)}")
        return False
    
    if missing_optional:
        print(f"⚠️  Missing optional environment variables: {', '.join(missing_optional)}")
        print("   These features will use fallback modes:")
        if 'QDRANT_URL' in missing_optional or 'QDRANT_API_KEY' in missing_optional:
            print("   - RAG system will use built-in knowledge base")
        if 'BRAVE_API_KEY' in missing_optional:
            print("   - Web search will use fallback mode")
    
    return True

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = [
        'streamlit', 'openai', 'python-dotenv', 'requests',
        'PyPDF2', 'python-docx', 'openpyxl', 'Pillow'
    ]
    
    optional_packages = [
        'qdrant-client', 'pytesseract', 'langchain', 'tiktoken'
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}: Installed")
        except ImportError:
            missing_required.append(package)
    
    for package in optional_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}: Installed")
        except ImportError:
            missing_optional.append(package)
    
    if missing_required:
        print(f"❌ Missing required packages: {', '.join(missing_required)}")
        print("   Install with: pip install " + " ".join(missing_required))
        return False
    
    if missing_optional:
        print(f"⚠️  Missing optional packages: {', '.join(missing_optional)}")
        print("   Install with: pip install " + " ".join(missing_optional))
        print("   These packages enable enhanced features")
    
    return True

def create_temp_directory():
    """Create temporary directory for file processing"""
    temp_dir = Path('temp')
    if not temp_dir.exists():
        temp_dir.mkdir()
        print("✅ Created temp directory for file processing")
    else:
        print("✅ Temp directory already exists")

def validate_setup():
    """Run complete setup validation"""
    print("🚀 Multimodal Agentic RAG Chatbot - Setup Validation")
    print("=" * 60)
    
    checks = [
        ("Python Version", check_python_version),
        ("Environment Configuration", check_environment_file),
        ("Dependencies", check_dependencies),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n📋 Checking {check_name}...")
        if not check_func():
            all_passed = False
    
    print(f"\n📁 Setting up directories...")
    create_temp_directory()
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 Setup validation completed successfully!")
        print("\n🚀 You can now run the chatbot with:")
        print("   streamlit run app.py")
        print("\n💡 Features available:")
        print("   - RAG-first document analysis")
        print("   - Multi-format document processing")
        print("   - Intelligent query routing")
        print("   - Chain-of-thought reasoning")
        
        if os.getenv('QDRANT_URL') and os.getenv('QDRANT_API_KEY'):
            print("   - Vector database RAG (Qdrant)")
        else:
            print("   - Built-in knowledge base (fallback)")
            
        if os.getenv('BRAVE_API_KEY'):
            print("   - Real-time web search")
        else:
            print("   - Web search fallback mode")
    else:
        print("❌ Setup validation failed!")
        print("   Please fix the issues above before running the chatbot.")
        return False
    
    return True

def install_requirements():
    """Install requirements from requirements.txt"""
    requirements_file = Path('requirements.txt')
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    print("📦 Installing requirements...")
    os.system(f"{sys.executable} -m pip install -r requirements.txt")
    print("✅ Requirements installation completed")
    return True

def main():
    """Main setup function"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "install":
            install_requirements()
        elif sys.argv[1] == "validate":
            validate_setup()
        else:
            print("Usage: python setup.py [install|validate]")
    else:
        validate_setup()

if __name__ == "__main__":
    main()
