"""
RAG System with Document Context Prioritization
Retrieval-Augmented Generation system that prioritizes domain knowledge
with intelligent document context integration
"""

import os
import logging
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams
except ImportError:
    print("Warning: qdrant-client not installed. RAG functionality will be limited.")
    QdrantClient = None

from openai import OpenAI

load_dotenv()

class RAGSystem:
    """
    RAG system implementing knowledge base prioritization with document context support
    """
    
    def __init__(self):
        """Initialize RAG system with Qdrant and OpenAI clients"""
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.logger = logging.getLogger(__name__)
        
        # Initialize Qdrant client if available
        self.qdrant_client = None
        if QdrantClient and os.getenv('QDRANT_URL') and os.getenv('QDRANT_API_KEY'):
            try:
                self.qdrant_client = QdrantClient(
                    url=os.getenv('QDRANT_URL'),
                    api_key=os.getenv('QDRANT_API_KEY')
                )
                # Use specific collection names as provided
                self.collection_names = ["TAX-RAG-1", "tax_documents"]
                self.logger.info(f"Qdrant client initialized successfully with collections: {self.collection_names}")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Qdrant client: {str(e)}")
                self.qdrant_client = None
        else:
            self.logger.warning("Qdrant configuration not found. Using fallback mode.")

    def test_collections(self):
        """Test connectivity and configuration of both collections"""
        if not self.qdrant_client:
            self.logger.warning("Qdrant client not available for testing")
            return False

        test_results = {}

        for collection_name in self.collection_names:
            try:
                # Get collection info
                collection_info = self.qdrant_client.get_collection(collection_name)
                self.logger.info(f"Collection {collection_name} exists with {collection_info.points_count} points")
                self.logger.info(f"Collection config: {collection_info.config}")

                # Test vector search with a simple query
                test_embedding = [0.1] * 1536  # Standard OpenAI embedding size

                # Try search with appropriate vector configuration
                try:
                    if collection_name == "TAX-RAG-1":
                        # TAX-RAG-1 requires named vector specification
                        search_params = {
                            "collection_name": collection_name,
                            "query_vector": ("text-dense", test_embedding),
                            "limit": 1
                        }
                    else:
                        # tax_documents uses basic vector search
                        search_params = {
                            "collection_name": collection_name,
                            "query_vector": test_embedding,
                            "limit": 1
                        }

                    results = self.qdrant_client.search(**search_params)
                    self.logger.info(f"✅ {collection_name} search successful: {len(results)} results")
                    test_results[f"{collection_name}_basic"] = True

                except Exception as search_error:
                    self.logger.warning(f"❌ {collection_name} search failed: {search_error}")
                    test_results[f"{collection_name}_basic"] = False

            except Exception as e:
                self.logger.error(f"❌ Collection {collection_name} test failed: {str(e)}")
                test_results[collection_name] = False

        return test_results

    def _is_content_relevant_to_query(self, query: str, context_docs: List[str]) -> bool:
        """
        Check if retrieved content is actually relevant to the user's query

        Args:
            query: User's question
            context_docs: Retrieved content documents

        Returns:
            True if content appears relevant to the query
        """
        if not context_docs:
            return False

        # Extract key terms from the query
        query_lower = query.lower()

        # Define domain-specific term categories
        banking_terms = [
            'credit', 'loan', 'advance', 'facility', 'revolving', 'portfolio',
            'classification', 'provisioning', 'npa', 'non-performing', 'bank',
            'lending', 'borrower', 'exposure', 'risk', 'collateral'
        ]

        audit_terms = [
            'audit', 'auditor', 'verify', 'verification', 'assess', 'assessment',
            'procedure', 'compliance', 'review', 'examination', 'testing',
            'safeguard', 'control', 'reporting', 'requirement'
        ]

        tax_terms = [
            'tax', 'taxation', 'gst', 'income tax', 'deduction', 'exemption',
            'itr', 'return', 'filing', 'tds', 'tcs', 'section', 'act'
        ]

        legal_terms = [
            'legal', 'law', 'regulation', 'compliance', 'statutory',
            'company law', 'corporate', 'registration', 'license'
        ]

        # Combine all relevant terms
        all_relevant_terms = banking_terms + audit_terms + tax_terms + legal_terms

        # Check if query contains any relevant terms
        query_relevant_terms = [term for term in all_relevant_terms if term in query_lower]

        if not query_relevant_terms:
            # If query doesn't contain domain terms, be more lenient
            return True

        # Check if retrieved content contains terms related to the query
        content_text = ' '.join(context_docs).lower()

        # Count matches between query terms and content
        content_matches = [term for term in query_relevant_terms if term in content_text]

        # Require at least 20% of query terms to be present in content
        relevance_ratio = len(content_matches) / len(query_relevant_terms) if query_relevant_terms else 0

        self.logger.info(f"📊 Content relevance check: {len(content_matches)}/{len(query_relevant_terms)} terms matched (ratio: {relevance_ratio:.2f})")

        return relevance_ratio >= 0.2  # At least 20% of query terms should be in content

    def query(self, query: str, context: Dict) -> Dict[str, Any]:
        """
        Standard RAG query without document context

        Args:
            query: User's question
            context: Session context

        Returns:
            Dict containing response and source metadata
        """
        try:
            if self.qdrant_client:
                return self._query_with_vector_search(query, context)
            else:
                return self._query_fallback_mode(query, context)

        except Exception as e:
            self.logger.error(f"Error in RAG query: {str(e)}")
            return self._query_fallback_mode(query, context)
    
    def query_with_document_context(self, query: str, document_content: str, context: Dict) -> str:
        """
        Enhanced RAG query that prioritizes domain knowledge with document context
        
        Args:
            query: User's question
            document_content: Content from uploaded document
            context: Session context
            
        Returns:
            RAG response prioritizing knowledge base with document context
        """
        try:
            if self.qdrant_client:
                return self._query_with_document_and_vectors(query, document_content, context)
            else:
                return self._query_with_document_fallback(query, document_content, context)
                
        except Exception as e:
            self.logger.error(f"Error in RAG query with document context: {str(e)}")
            return self._query_with_document_fallback(query, document_content, context)
    
    def _query_with_vector_search(self, query: str, context: Dict) -> Dict[str, Any]:
        """
        Query using vector search in both Qdrant collections

        Args:
            query: User's question
            context: Session context

        Returns:
            Dict containing response and detailed source metadata
        """
        self.logger.info(f"🔍 Starting vector search for query: {query[:100]}...")

        # Generate query embedding
        query_embedding = self.get_embedding(query)
        if not query_embedding:
            self.logger.error("❌ Failed to generate query embedding")
            return self._query_fallback_mode(query, context)

        self.logger.info(f"✅ Generated embedding with {len(query_embedding)} dimensions")

        # Search in both collections with proper vector handling
        all_results = []
        sources_used = []

        for collection_name in self.collection_names:
            self.logger.info(f"🔍 Searching collection: {collection_name}")
            try:
                search_results = []

                # Handle different vector configurations for each collection
                if collection_name == "TAX-RAG-1":
                    # TAX-RAG-1 requires named vector specification (only text-dense exists)
                    try:
                        search_results = self.qdrant_client.search(
                            collection_name=collection_name,
                            query_vector=("text-dense", query_embedding),
                            limit=5,  # Increased limit
                            with_payload=True,
                            with_vectors=False,
                            score_threshold=0.0  # Remove threshold to get all results
                        )
                        self.logger.info(f"✅ text-dense search successful for {collection_name}: {len(search_results)} results")

                        # Log scores for debugging
                        if search_results:
                            scores = [f"{r.score:.4f}" for r in search_results[:3]]
                            self.logger.info(f"📊 Top scores from {collection_name}: {scores}")

                    except Exception as dense_error:
                        self.logger.error(f"❌ text-dense search failed for {collection_name}: {dense_error}")
                        search_results = []

                else:
                    # tax_documents collection - use basic search
                    try:
                        search_results = self.qdrant_client.search(
                            collection_name=collection_name,
                            query_vector=query_embedding,
                            limit=5,  # Increased limit
                            with_payload=True,
                            with_vectors=False,
                            score_threshold=0.0  # Remove threshold to get all results
                        )
                        self.logger.info(f"✅ Basic search successful for {collection_name}: {len(search_results)} results")

                        # Log scores for debugging
                        if search_results:
                            scores = [f"{r.score:.4f}" for r in search_results[:3]]
                            self.logger.info(f"📊 Top scores from {collection_name}: {scores}")

                    except Exception as basic_error:
                        self.logger.error(f"❌ Basic search failed for {collection_name}: {basic_error}")
                        search_results = []

                # Process search results with proper payload handling
                if search_results:
                    collection_results = []
                    for i, result in enumerate(search_results):
                        # Handle different payload structures
                        content = ""

                        if collection_name == "TAX-RAG-1":
                            # TAX-RAG-1 has complex JSON structure in _node_content
                            if hasattr(result, 'payload') and result.payload:
                                content = self._extract_tax_rag_content(result.payload)
                                self.logger.debug(f"TAX-RAG-1 result {i}: extracted content length = {len(content) if content else 0}")
                        else:
                            # tax_documents has page_content
                            if hasattr(result, 'payload') and result.payload:
                                content = (result.payload.get('page_content') or
                                          result.payload.get('content') or
                                          result.payload.get('text') or
                                          str(result.payload))

                                self.logger.debug(f"tax_documents result {i}: payload keys = {list(result.payload.keys())}")

                        if content and content.strip():
                            collection_results.append({
                                'content': content.strip(),
                                'source': collection_name,
                                'score': result.score
                            })
                            self.logger.debug(f"✅ Added result from {collection_name}: score={result.score:.4f}, content_length={len(content)}")
                        else:
                            self.logger.warning(f"⚠️ Empty content from {collection_name} result {i}")

                    if collection_results:
                        all_results.extend(collection_results)
                        sources_used.append(collection_name)
                        self.logger.info(f"✅ Successfully processed {len(collection_results)} results from {collection_name}")
                    else:
                        self.logger.warning(f"⚠️ No usable content found in {collection_name} results")
                else:
                    self.logger.warning(f"⚠️ No search results found in collection {collection_name}")

            except Exception as e:
                self.logger.error(f"❌ Unexpected error searching collection {collection_name}: {str(e)}")

        # Log overall results
        self.logger.info(f"📊 Total results collected: {len(all_results)} from sources: {sources_used}")

        if not all_results:
            self.logger.warning("⚠️ No results found in any collection, using fallback mode")
            return self._query_fallback_mode(query, context)

        # Sort by relevance score and take top 5
        all_results.sort(key=lambda x: x['score'], reverse=True)
        top_results = all_results[:5]

        # Log final selection
        final_scores = [f"{r['score']:.4f}" for r in top_results]
        self.logger.info(f"📊 Final top results scores: {final_scores}")

        # Check if we have meaningful results (improved relevance threshold)
        min_relevance_threshold = 0.15  # Increased from 0.0 to filter out very irrelevant results
        relevant_results = [r for r in top_results if r['score'] >= min_relevance_threshold]

        if not relevant_results:
            self.logger.warning(f"⚠️ No results above relevance threshold {min_relevance_threshold}, using fallback mode")
            return self._query_fallback_mode(query, context)

        # Extract content for response generation
        context_docs = [result['content'] for result in relevant_results if result['content'].strip()]

        if not context_docs:
            self.logger.warning("⚠️ No valid content in relevant results, using fallback mode")
            return self._query_fallback_mode(query, context)

        # Additional content relevance check
        if not self._is_content_relevant_to_query(query, context_docs):
            self.logger.warning("⚠️ Retrieved content not relevant to query, using fallback mode")
            return self._query_fallback_mode(query, context)

        self.logger.info(f"✅ Generating response with {len(context_docs)} relevant context documents")

        # Generate response with detailed source metadata
        response_text = self.generate_response_with_sources(query, context_docs, context, sources_used, "RAG")

        # Create detailed source metadata
        source_metadata = {
            'response_type': 'RAG',
            'sources_used': sources_used,
            'total_results': len(all_results),
            'results_by_source': {source: len([r for r in all_results if r['source'] == source]) for source in sources_used},
            'top_scores': [r['score'] for r in top_results],
            'context_docs_count': len(context_docs),
            'qdrant_available': True,
            'fallback_used': False
        }

        return {
            'response': response_text,
            'metadata': source_metadata
        }
    
    def _query_with_document_and_vectors(self, query: str, document_content: str, context: Dict) -> str:
        """
        Query using both vector search and document context

        Args:
            query: User's question
            document_content: Document content
            context: Session context

        Returns:
            RAG response with document context prioritization and source attribution
        """
        self.logger.info(f"🔍 Starting document+vector search for query: {query[:100]}...")

        # Generate query embedding
        query_embedding = self.get_embedding(query)
        if not query_embedding:
            self.logger.error("❌ Failed to generate query embedding")
            return self._query_with_document_fallback(query, document_content, context)

        self.logger.info(f"✅ Generated embedding with {len(query_embedding)} dimensions")

        # Search in both collections with proper vector handling
        all_results = []
        sources_used = []

        for collection_name in self.collection_names:
            self.logger.info(f"🔍 Searching collection: {collection_name}")
            try:
                search_results = []

                # Handle different vector configurations for each collection
                if collection_name == "TAX-RAG-1":
                    # TAX-RAG-1 requires named vector specification (only text-dense exists)
                    try:
                        search_results = self.qdrant_client.search(
                            collection_name=collection_name,
                            query_vector=("text-dense", query_embedding),
                            limit=5,  # Increased limit
                            with_payload=True,
                            with_vectors=False,
                            score_threshold=0.0  # Remove threshold to get all results
                        )
                        self.logger.info(f"✅ text-dense search successful for {collection_name}: {len(search_results)} results")

                        # Log scores for debugging
                        if search_results:
                            scores = [f"{r.score:.4f}" for r in search_results[:3]]
                            self.logger.info(f"📊 Top scores from {collection_name}: {scores}")

                    except Exception as dense_error:
                        self.logger.error(f"❌ text-dense search failed for {collection_name}: {dense_error}")
                        search_results = []

                else:
                    # tax_documents collection - use basic search
                    try:
                        search_results = self.qdrant_client.search(
                            collection_name=collection_name,
                            query_vector=query_embedding,
                            limit=5,  # Increased limit
                            with_payload=True,
                            with_vectors=False,
                            score_threshold=0.0  # Remove threshold to get all results
                        )
                        self.logger.info(f"✅ Basic search successful for {collection_name}: {len(search_results)} results")

                        # Log scores for debugging
                        if search_results:
                            scores = [f"{r.score:.4f}" for r in search_results[:3]]
                            self.logger.info(f"📊 Top scores from {collection_name}: {scores}")

                    except Exception as basic_error:
                        self.logger.error(f"❌ Basic search failed for {collection_name}: {basic_error}")
                        search_results = []

                # Process search results with proper payload handling
                if search_results:
                    collection_results = []
                    for i, result in enumerate(search_results):
                        # Handle different payload structures
                        content = ""

                        if collection_name == "TAX-RAG-1":
                            # TAX-RAG-1 has complex JSON structure in _node_content
                            if hasattr(result, 'payload') and result.payload:
                                content = self._extract_tax_rag_content(result.payload)
                                self.logger.debug(f"TAX-RAG-1 result {i}: extracted content length = {len(content) if content else 0}")
                        else:
                            # tax_documents has page_content
                            if hasattr(result, 'payload') and result.payload:
                                content = (result.payload.get('page_content') or
                                          result.payload.get('content') or
                                          result.payload.get('text') or
                                          str(result.payload))

                                self.logger.debug(f"tax_documents result {i}: payload keys = {list(result.payload.keys())}")

                        if content and content.strip():
                            collection_results.append({
                                'content': content.strip(),
                                'source': collection_name,
                                'score': result.score
                            })
                            self.logger.debug(f"✅ Added result from {collection_name}: score={result.score:.4f}, content_length={len(content)}")
                        else:
                            self.logger.warning(f"⚠️ Empty content from {collection_name} result {i}")

                    if collection_results:
                        all_results.extend(collection_results)
                        sources_used.append(collection_name)
                        self.logger.info(f"✅ Successfully processed {len(collection_results)} results from {collection_name}")
                    else:
                        self.logger.warning(f"⚠️ No usable content found in {collection_name} results")
                else:
                    self.logger.warning(f"⚠️ No search results found in collection {collection_name}")

            except Exception as e:
                self.logger.error(f"❌ Unexpected error searching collection {collection_name}: {str(e)}")

        # Log overall results
        self.logger.info(f"📊 Total results collected: {len(all_results)} from sources: {sources_used}")

        if not all_results:
            self.logger.warning("⚠️ No results found in any collection, using document fallback mode")
            return self._query_with_document_fallback(query, document_content, context)

        # Sort by relevance score and take top 5
        all_results.sort(key=lambda x: x['score'], reverse=True)
        top_results = all_results[:5]

        # Log final selection
        final_scores = [f"{r['score']:.4f}" for r in top_results]
        self.logger.info(f"📊 Final top results scores: {final_scores}")

        # Check if we have meaningful results (improved relevance threshold)
        min_relevance_threshold = 0.15  # Same threshold as main query method
        relevant_results = [r for r in top_results if r['score'] >= min_relevance_threshold]

        if not relevant_results:
            self.logger.warning(f"⚠️ No results above relevance threshold {min_relevance_threshold}, using document fallback mode")
            return self._query_with_document_fallback(query, document_content, context)

        # Extract content for response generation
        context_docs = [result['content'] for result in relevant_results if result['content'].strip()]

        if not context_docs:
            self.logger.warning("⚠️ No valid content in relevant results, using document fallback mode")
            return self._query_with_document_fallback(query, document_content, context)

        # Additional content relevance check
        if not self._is_content_relevant_to_query(query, context_docs):
            self.logger.warning("⚠️ Retrieved content not relevant to query, using document fallback mode")
            return self._query_with_document_fallback(query, document_content, context)

        self.logger.info(f"✅ Generating response with {len(context_docs)} context documents + document content")

        # Generate response prioritizing RAG knowledge with document context
        return self.generate_response_with_document_context_and_sources(
            query, context_docs, document_content, context, sources_used
        )
    
    def _query_fallback_mode(self, query: str, context: Dict) -> Dict[str, Any]:
        """
        Fallback query mode when Qdrant is not available

        Args:
            query: User's question
            context: Session context

        Returns:
            Dict containing response and source metadata
        """
        # Use built-in CA knowledge for common queries
        ca_knowledge = self._get_builtin_ca_knowledge(query)

        if ca_knowledge:
            response_text = self.generate_response(query, [ca_knowledge], context)
            source_metadata = {
                'response_type': 'BUILTIN_KNOWLEDGE',
                'sources_used': ['builtin_ca_knowledge'],
                'qdrant_available': False,
                'fallback_used': True,
                'builtin_knowledge_used': True
            }
        else:
            response_text = self._generate_direct_response(query, context)
            source_metadata = {
                'response_type': 'LLM_ONLY',
                'sources_used': ['llm_knowledge'],
                'qdrant_available': False,
                'fallback_used': True,
                'builtin_knowledge_used': False
            }

        return {
            'response': response_text,
            'metadata': source_metadata
        }
    
    def _query_with_document_fallback(self, query: str, document_content: str, context: Dict) -> str:
        """
        Fallback query with document when Qdrant is not available
        
        Args:
            query: User's question
            document_content: Document content
            context: Session context
            
        Returns:
            Response using built-in knowledge and document
        """
        ca_knowledge = self._get_builtin_ca_knowledge(query)
        
        if ca_knowledge:
            return self.generate_response_with_document_context(
                query, [ca_knowledge], document_content, context
            )
        else:
            return self._generate_direct_response_with_document(query, document_content, context)
    
    def _extract_tax_rag_content(self, payload: Dict) -> str:
        """
        Extract actual text content from TAX-RAG-1 complex JSON structure

        Args:
            payload: The payload from TAX-RAG-1 collection

        Returns:
            Extracted text content
        """
        try:
            # TAX-RAG-1 stores content in _node_content as JSON string
            if '_node_content' in payload:
                import json
                node_content_str = payload['_node_content']

                # Parse the JSON string
                node_content = json.loads(node_content_str)

                # Extract the actual text content
                if 'text' in node_content:
                    text_content = node_content['text']
                    self.logger.debug(f"Extracted text content: {len(text_content)} characters")
                    return text_content
                else:
                    self.logger.warning("No 'text' field found in _node_content")

            # Fallback to file path if no content found
            file_path = payload.get('file_path', 'Unknown document')
            return f"Content from: {file_path}"

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse _node_content JSON: {str(e)}")
            return f"Document: {payload.get('file_path', 'Unknown')}"
        except Exception as e:
            self.logger.error(f"Error extracting TAX-RAG-1 content: {str(e)}")
            return f"Document: {payload.get('file_path', 'Unknown')}"

    def get_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for text using OpenAI

        Args:
            text: Text to embed

        Returns:
            Embedding vector
        """
        try:
            response = self.openai_client.embeddings.create(
                input=text,
                model="text-embedding-ada-002"
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"Error generating embedding: {str(e)}")
            return []
    
    def generate_response(self, query: str, context_docs: List[str], context: Dict) -> str:
        """
        Generate response using RAG context
        
        Args:
            query: User's question
            context_docs: Retrieved documents from knowledge base
            context: Session context
            
        Returns:
            Generated response
        """
        # Prepare context from retrieved documents
        rag_context = self.format_rag_docs(context_docs)
        conversation_context = self.format_conversation_context(context)
        
        prompt = f"""You are a professional CA assistant with expertise in tax, legal, and business matters.

KNOWLEDGE BASE INFORMATION:
{rag_context}

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

INSTRUCTIONS:
1. Carefully analyze if the knowledge base information is directly relevant to the user's specific query
2. If the information is relevant and helpful, provide a detailed, technical response using that information
3. If the information is not directly relevant or helpful for this specific query, clearly state: "The knowledge base does not contain specific information about [topic]. However, based on my professional expertise..."
4. For technical queries (audit, compliance, banking, classification, provisioning, etc.), provide specific procedures, steps, and regulatory references when available
5. Focus on practical, actionable guidance rather than generic consulting frameworks
6. Be honest about the limitations of the available information

Please provide a comprehensive, expert-level answer to the user's query."""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant. Provide accurate, helpful advice based on the provided knowledge base."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"Error generating response: {str(e)}")
            return "I'm experiencing technical difficulties. Please try again."

    def generate_response_with_sources(self, query: str, context_docs: List[str], context: Dict, sources_used: List[str], response_type: str) -> str:
        """
        Generate response with source attribution

        Args:
            query: User's question
            context_docs: Retrieved documents from knowledge base
            context: Session context
            sources_used: List of sources used (collection names)
            response_type: Type of response (RAG, LLM, WEB_SEARCH)

        Returns:
            Generated response with source attribution
        """
        # Prepare context from retrieved documents
        rag_context = self.format_rag_docs_with_sources(context_docs, sources_used)
        conversation_context = self.format_conversation_context(context)

        prompt = f"""You are a specialized CA assistant with deep expertise in tax, legal, audit, and business matters. You have access to a comprehensive knowledge base of CA-related information.

KNOWLEDGE BASE INFORMATION:
{rag_context}

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

INSTRUCTIONS:
1. Carefully analyze if the knowledge base information is directly relevant to the user's specific query
2. If the information is relevant and helpful, provide a detailed, technical response using that information
3. If the information is not directly relevant or helpful for this specific query, clearly state: "The knowledge base does not contain specific information about [topic]. However, based on my professional expertise..."
4. For technical queries (audit, compliance, banking, classification, provisioning, etc.), provide specific procedures, steps, and regulatory references when available
5. Focus on practical, actionable guidance rather than generic consulting frameworks
6. Be honest about the limitations of the available information while still providing helpful professional guidance
7. Maintain professional tone and provide authoritative advice based on CA expertise

Please provide a comprehensive, expert-level answer to the user's query."""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a specialized CA assistant with deep expertise in tax, audit, legal, and business matters. Provide authoritative, comprehensive advice drawing from your knowledge base and professional expertise."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            generated_response = response.choices[0].message.content

            # Add source attribution
            source_info = self._format_source_attribution(response_type, sources_used)
            return f"{generated_response}\n\n{source_info}"

        except Exception as e:
            self.logger.error(f"Error generating response with sources: {str(e)}")
            return "I'm experiencing technical difficulties. Please try again."

    def generate_response_with_document_context(self, query: str, rag_docs: List[str],
                                               document_content: str, context: Dict) -> str:
        """
        Generate response prioritizing RAG knowledge with document as supplementary context

        Args:
            query: User's question
            rag_docs: Retrieved documents from knowledge base
            document_content: Content from uploaded document
            context: Session context

        Returns:
            Generated response with RAG prioritization
        """
        # Prepare enhanced prompt that prioritizes RAG knowledge
        rag_context = self.format_rag_docs(rag_docs)
        conversation_context = self.format_conversation_context(context)

        prompt = f"""You are a professional CA assistant with expertise in tax, legal, and business matters.

INSTRUCTIONS:
1. PRIMARILY use information from the Knowledge Base (most authoritative)
2. Use the uploaded document as SUPPLEMENTARY context only
3. If Knowledge Base has relevant information, lead with that
4. Cross-reference document details with established knowledge
5. If document contradicts knowledge base, note the discrepancy
6. Always maintain professional CA advisory tone

KNOWLEDGE BASE INFORMATION:
{rag_context}

UPLOADED DOCUMENT CONTEXT:
{document_content[:1500]}...

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

Please provide a comprehensive answer that:
- Leads with authoritative knowledge base information
- References specific document details where relevant
- Explains any discrepancies between sources
- Provides actionable CA advice"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant. Always prioritize established knowledge base information over document content when providing advice."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            self.logger.error(f"Error generating response with document context: {str(e)}")
            return "I'm experiencing technical difficulties processing your document query. Please try again."

    def generate_response_with_document_context_and_sources(self, query: str, rag_docs: List[str],
                                                           document_content: str, context: Dict, sources_used: List[str]) -> str:
        """
        Generate response prioritizing RAG knowledge with document context and source attribution

        Args:
            query: User's question
            rag_docs: Retrieved documents from knowledge base
            document_content: Content from uploaded document
            context: Session context
            sources_used: List of RAG sources used

        Returns:
            Generated response with RAG prioritization and source attribution
        """
        # Prepare enhanced prompt that prioritizes RAG knowledge
        rag_context = self.format_rag_docs_with_sources(rag_docs, sources_used)
        conversation_context = self.format_conversation_context(context)

        prompt = f"""You are a professional CA assistant with expertise in tax, legal, and business matters.

INSTRUCTIONS:
1. PRIMARILY use information from the Knowledge Base (most authoritative)
2. Use the uploaded document as SUPPLEMENTARY context only
3. If Knowledge Base has relevant information, lead with that
4. Cross-reference document details with established knowledge
5. If document contradicts knowledge base, note the discrepancy
6. Always maintain professional CA advisory tone

KNOWLEDGE BASE INFORMATION:
{rag_context}

UPLOADED DOCUMENT CONTEXT:
{document_content[:1500]}...

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

Please provide a comprehensive answer that:
- Leads with authoritative knowledge base information
- References specific document details where relevant
- Explains any discrepancies between sources
- Provides actionable CA advice"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant. Always prioritize established knowledge base information over document content when providing advice."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            generated_response = response.choices[0].message.content

            # Add source attribution for RAG + Document
            source_info = self._format_source_attribution("RAG_DOCUMENT", sources_used)
            return f"{generated_response}\n\n{source_info}"

        except Exception as e:
            self.logger.error(f"Error generating response with document context and sources: {str(e)}")
            return "I'm experiencing technical difficulties processing your document query. Please try again."

    def format_rag_docs(self, docs: List[str]) -> str:
        """Format RAG documents for prompt"""
        if not docs:
            return "No relevant information found in knowledge base."

        formatted_docs = ""
        for i, doc in enumerate(docs, 1):
            if doc.strip():
                formatted_docs += f"Source {i}: {doc[:500]}...\n\n"

        return formatted_docs if formatted_docs else "No relevant information found in knowledge base."

    def format_rag_docs_with_sources(self, docs: List[str], sources_used: List[str]) -> str:
        """Format RAG documents with source information for prompt"""
        if not docs:
            return "No relevant information found in knowledge base."

        formatted_docs = ""
        for i, doc in enumerate(docs, 1):
            if doc.strip():
                # Determine which source this doc likely came from
                source_info = f" (from {sources_used[0] if sources_used else 'knowledge base'})"
                formatted_docs += f"Source {i}{source_info}: {doc[:500]}...\n\n"

        return formatted_docs if formatted_docs else "No relevant information found in knowledge base."

    def _format_source_attribution(self, response_type: str, sources_used: List[str] = None) -> str:
        """
        Format source attribution for user display

        Args:
            response_type: Type of response (RAG, LLM, WEB_SEARCH, RAG_DOCUMENT)
            sources_used: List of sources used

        Returns:
            Formatted source attribution string
        """
        if response_type == "RAG":
            if sources_used:
                sources_str = ", ".join(sources_used)
                return f"📚 **Source**: Knowledge Base (Collections: {sources_str})"
            else:
                return f"📚 **Source**: Knowledge Base (Built-in CA Knowledge)"

        elif response_type == "RAG_DOCUMENT":
            if sources_used:
                sources_str = ", ".join(sources_used)
                return f"📚 **Source**: Knowledge Base (Collections: {sources_str}) + 📄 Uploaded Document"
            else:
                return f"📚 **Source**: Knowledge Base (Built-in CA Knowledge) + 📄 Uploaded Document"

        elif response_type == "LLM":
            return f"🤖 **Source**: AI Analysis (General Knowledge)"

        elif response_type == "LLM_DOCUMENT":
            return f"🤖 **Source**: AI Analysis + 📄 Uploaded Document"

        elif response_type == "WEB_SEARCH":
            return f"🌐 **Source**: Web Search (Real-time Information)"

        elif response_type == "HYBRID":
            if sources_used:
                sources_str = ", ".join(sources_used)
                return f"🔄 **Source**: Hybrid (Knowledge Base: {sources_str} + AI Analysis + 📄 Document)"
            else:
                return f"🔄 **Source**: Hybrid (Built-in Knowledge + AI Analysis + 📄 Document)"

        else:
            return f"ℹ️ **Source**: {response_type}"

    def format_conversation_context(self, context: Dict) -> str:
        """Format conversation context for prompt"""
        if not context.get('context'):
            return "No previous context"

        recent_context = context['context'][-3:]  # Last 3 exchanges
        formatted_context = ""
        for exchange in recent_context:
            formatted_context += f"Q: {exchange['query']}\nA: {exchange['response'][:200]}...\n\n"

        return formatted_context if formatted_context else "No previous context"

    def _get_builtin_ca_knowledge(self, query: str) -> Optional[str]:
        """
        Get built-in CA knowledge for common queries when Qdrant is not available

        Args:
            query: User's question

        Returns:
            Relevant CA knowledge or None
        """
        query_lower = query.lower()

        # Basic CA knowledge base for fallback
        knowledge_base = {
            'tax deduction': """Common tax deductions under Income Tax Act:
            - Section 80C: Life insurance, PPF, ELSS (up to ₹1.5 lakh)
            - Section 80D: Health insurance premiums
            - Section 24: Home loan interest
            - Section 80E: Education loan interest
            - Section 80G: Donations to charitable organizations""",

            'gst': """GST (Goods and Services Tax) basics:
            - Standard rate: 18%
            - Essential goods: 5%
            - Luxury items: 28%
            - Registration required if turnover > ₹40 lakh (₹20 lakh for special states)
            - Monthly/quarterly returns filing required""",

            'company registration': """Company registration process:
            1. Obtain Digital Signature Certificate (DSC)
            2. Apply for Director Identification Number (DIN)
            3. Reserve company name
            4. File incorporation documents with ROC
            5. Obtain Certificate of Incorporation
            6. Apply for PAN and TAN""",

            'itr filing': """ITR filing requirements:
            - Due date: July 31st for individuals
            - Required documents: Form 16, bank statements, investment proofs
            - Different ITR forms for different income sources
            - Late filing penalty: ₹5,000 (₹1,000 for income < ₹5 lakh)"""
        }

        for keyword, knowledge in knowledge_base.items():
            if keyword in query_lower:
                return knowledge

        return None

    def _generate_direct_response(self, query: str, context: Dict) -> str:
        """Generate direct response without RAG context with source attribution"""
        conversation_context = self.format_conversation_context(context)

        prompt = f"""You are a specialized CA assistant with deep expertise in tax, audit, legal, and business matters in India. Provide authoritative, comprehensive guidance based on your professional knowledge.

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

INSTRUCTIONS:
1. Provide expert-level, authoritative answers
2. Draw upon comprehensive CA knowledge and best practices
3. Include relevant standards, regulations, or procedures when applicable
4. Focus on practical, actionable guidance
5. Maintain professional tone appropriate for CA practice

Provide a comprehensive, expert-level response to the user's query."""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a specialized CA assistant with deep expertise in Indian tax, audit, legal, and business matters. Provide authoritative, comprehensive advice based on your professional knowledge and experience."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=800
            )

            generated_response = response.choices[0].message.content
            source_info = self._format_source_attribution("LLM")
            return f"{generated_response}\n\n{source_info}"

        except Exception as e:
            self.logger.error(f"Error generating direct response: {str(e)}")
            return "I'm experiencing technical difficulties. Please try again later."

    def _generate_direct_response_with_document(self, query: str, document_content: str, context: Dict) -> str:
        """Generate direct response with document context and source attribution"""
        conversation_context = self.format_conversation_context(context)

        prompt = f"""You are a professional CA assistant. Answer the query based on the document content and your CA expertise.

DOCUMENT CONTENT:
{document_content[:2000]}...

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

Provide analysis based on the document and your CA knowledge."""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant. Analyze documents with your expertise."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            generated_response = response.choices[0].message.content
            source_info = self._format_source_attribution("LLM_DOCUMENT")
            return f"{generated_response}\n\n{source_info}"

        except Exception as e:
            self.logger.error(f"Error generating direct response with document: {str(e)}")
            return "I'm experiencing technical difficulties analyzing the document. Please try again."


if __name__ == "__main__":
    # Simple test
    rag_system = RAGSystem()
    print("RAG System initialized successfully!")

    # Test basic query
    response = rag_system.query("What are tax deductions?", {})
    print(f"Response: {response[:200]}...")
