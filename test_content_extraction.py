"""
Test content extraction from TAX-RAG-1 collection
"""

import os
import sys
import json
import logging
from dotenv import load_dotenv

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_content_extraction():
    """Test the content extraction from TAX-RAG-1"""
    print("🧪 Testing Content Extraction from TAX-RAG-1")
    print("=" * 60)
    
    try:
        from rag_system import RAGSystem
        
        rag_system = RAGSystem()
        
        # Test with a simple query that should find content
        query = "tax"
        print(f"🔍 Testing query: '{query}'")
        
        # Generate embedding
        embedding = rag_system.get_embedding(query)
        if not embedding:
            print("❌ Failed to generate embedding")
            return
        
        print(f"✅ Generated embedding with {len(embedding)} dimensions")
        
        # Search TAX-RAG-1 directly
        try:
            results = rag_system.qdrant_client.search(
                collection_name="TAX-RAG-1",
                query_vector=("text-dense", embedding),
                limit=3,
                with_payload=True,
                score_threshold=0.0
            )
            
            print(f"✅ Found {len(results)} results from TAX-RAG-1")
            
            for i, result in enumerate(results):
                print(f"\n📋 Result {i+1}:")
                print(f"   Score: {result.score:.4f}")
                
                if result.payload:
                    print(f"   Payload keys: {list(result.payload.keys())}")
                    
                    # Test content extraction
                    extracted_content = rag_system._extract_tax_rag_content(result.payload)
                    
                    if extracted_content:
                        print(f"   ✅ Extracted content length: {len(extracted_content)}")
                        print(f"   Content preview: {extracted_content[:200]}...")
                        
                        # Check if it's meaningful content
                        if len(extracted_content) > 100 and not extracted_content.startswith("Document:"):
                            print(f"   ✅ Meaningful content extracted!")
                        else:
                            print(f"   ⚠️ Content seems to be just metadata")
                    else:
                        print(f"   ❌ No content extracted")
                        
                    # Show raw _node_content for debugging
                    if '_node_content' in result.payload:
                        node_content_str = result.payload['_node_content']
                        print(f"   Raw _node_content length: {len(node_content_str)}")
                        
                        try:
                            node_content = json.loads(node_content_str)
                            if 'text' in node_content:
                                text_content = node_content['text']
                                print(f"   ✅ Found 'text' field with {len(text_content)} characters")
                                print(f"   Text preview: {text_content[:200]}...")
                            else:
                                print(f"   ⚠️ No 'text' field in node_content")
                                print(f"   Available keys: {list(node_content.keys())}")
                        except json.JSONDecodeError as e:
                            print(f"   ❌ Failed to parse JSON: {str(e)}")
                else:
                    print(f"   ❌ No payload in result")
                    
        except Exception as e:
            print(f"❌ Search failed: {str(e)}")
            
        # Now test the full RAG query
        print(f"\n🔍 Testing full RAG query...")
        response = rag_system.query(query, {})
        
        print(f"📝 RAG Response length: {len(response)}")
        print(f"Response preview: {response[:300]}...")
        
        # Check if response indicates no content found
        if "knowledge base does not provide" in response.lower() or "no specific information" in response.lower():
            print("⚠️ RAG system still not finding content - content extraction may not be working")
        else:
            print("✅ RAG system found content!")
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

def test_tax_documents_extraction():
    """Test content extraction from tax_documents collection"""
    print(f"\n🧪 Testing Content Extraction from tax_documents")
    print("=" * 60)
    
    try:
        from rag_system import RAGSystem
        
        rag_system = RAGSystem()
        
        # Test with a simple query
        query = "tax"
        embedding = rag_system.get_embedding(query)
        
        # Search tax_documents directly
        results = rag_system.qdrant_client.search(
            collection_name="tax_documents",
            query_vector=embedding,
            limit=3,
            with_payload=True,
            score_threshold=0.0
        )
        
        print(f"✅ Found {len(results)} results from tax_documents")
        
        for i, result in enumerate(results):
            print(f"\n📋 Result {i+1}:")
            print(f"   Score: {result.score:.4f}")
            
            if result.payload:
                print(f"   Payload keys: {list(result.payload.keys())}")
                
                # Extract page_content
                if 'page_content' in result.payload:
                    content = result.payload['page_content']
                    print(f"   ✅ Page content length: {len(content)}")
                    print(f"   Content preview: {content[:200]}...")
                else:
                    print(f"   ❌ No page_content found")
                    
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")

def main():
    """Main test function"""
    print("🚀 CONTENT EXTRACTION TEST")
    print("=" * 70)
    
    # Check environment variables
    required_vars = ['OPENAI_API_KEY', 'QDRANT_URL', 'QDRANT_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return
    
    # Test TAX-RAG-1 content extraction
    test_content_extraction()
    
    # Test tax_documents content extraction
    test_tax_documents_extraction()
    
    print(f"\n📊 CONTENT EXTRACTION TEST COMPLETE")
    print("=" * 70)

if __name__ == "__main__":
    main()
