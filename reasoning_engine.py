"""
Chain of Thought Reasoning Engine
Provides step-by-step reasoning for complex CA queries
"""

import logging
from typing import Dict, Any, List
from openai import OpenAI

class ChainOfThoughtReasoning:
    """
    Reasoning engine for complex CA queries requiring step-by-step analysis
    """
    
    def __init__(self, openai_client: OpenAI):
        """
        Initialize reasoning engine
        
        Args:
            openai_client: OpenAI client instance
        """
        self.openai_client = openai_client
        self.logger = logging.getLogger(__name__)
    
    def apply_reasoning(self, query: str, context: Dict) -> str:
        """
        Apply chain of thought reasoning for complex queries
        
        Args:
            query: User's complex question
            context: Session context including conversation history
            
        Returns:
            Detailed response with step-by-step reasoning
        """
        try:
            # Determine the type of reasoning needed
            reasoning_type = self._classify_reasoning_type(query)
            
            if reasoning_type == "tax_calculation":
                return self._apply_tax_reasoning(query, context)
            elif reasoning_type == "legal_analysis":
                return self._apply_legal_reasoning(query, context)
            elif reasoning_type == "business_strategy":
                return self._apply_business_reasoning(query, context)
            elif reasoning_type == "compliance_check":
                return self._apply_compliance_reasoning(query, context)
            else:
                return self._apply_general_reasoning(query, context)
                
        except Exception as e:
            self.logger.error(f"Error in reasoning engine: {str(e)}")
            return self._fallback_reasoning(query, context)
    
    def _classify_reasoning_type(self, query: str) -> str:
        """
        Classify the type of reasoning needed
        
        Args:
            query: User's question
            
        Returns:
            Reasoning type classification
        """
        query_lower = query.lower()
        
        # Tax calculation keywords
        if any(word in query_lower for word in ['calculate', 'tax', 'deduction', 'exemption', 'itr', 'gst']):
            return "tax_calculation"
        
        # Legal analysis keywords
        elif any(word in query_lower for word in ['legal', 'compliance', 'law', 'regulation', 'agreement', 'contract']):
            return "legal_analysis"
        
        # Business strategy keywords
        elif any(word in query_lower for word in ['business', 'strategy', 'plan', 'recommend', 'should i', 'best approach']):
            return "business_strategy"
        
        # Compliance check keywords
        elif any(word in query_lower for word in ['comply', 'requirement', 'mandatory', 'filing', 'deadline']):
            return "compliance_check"
        
        else:
            return "general"
    
    def _apply_tax_reasoning(self, query: str, context: Dict) -> str:
        """
        Apply tax-specific reasoning
        
        Args:
            query: Tax-related question
            context: Session context
            
        Returns:
            Step-by-step tax analysis
        """
        reasoning_prompt = f"""You are a CA assistant providing step-by-step tax analysis.

Query: {query}
Context: {self._format_context(context)}

Please provide a detailed tax analysis following this structure:

1. **Understanding the Query**: What specific tax matter is being asked?
2. **Applicable Provisions**: Which sections/rules of tax law apply?
3. **Step-by-Step Calculation** (if applicable): Show the calculation process
4. **Key Considerations**: Important factors to consider
5. **Compliance Requirements**: Any filing or documentation needed
6. **Final Recommendation**: Clear actionable advice

Provide detailed reasoning for each step with relevant tax provisions."""
        
        return self._generate_reasoning_response(reasoning_prompt)
    
    def _apply_legal_reasoning(self, query: str, context: Dict) -> str:
        """
        Apply legal analysis reasoning
        
        Args:
            query: Legal question
            context: Session context
            
        Returns:
            Step-by-step legal analysis
        """
        reasoning_prompt = f"""You are a CA assistant providing step-by-step legal analysis.

Query: {query}
Context: {self._format_context(context)}

Please provide a detailed legal analysis following this structure:

1. **Legal Issue Identification**: What is the core legal question?
2. **Applicable Laws**: Which acts, rules, or regulations apply?
3. **Legal Analysis**: Step-by-step examination of the legal position
4. **Precedents/Guidelines**: Relevant case law or regulatory guidance
5. **Risk Assessment**: Potential legal risks or implications
6. **Recommended Action**: Clear legal advice and next steps

Provide thorough reasoning with legal citations where applicable."""
        
        return self._generate_reasoning_response(reasoning_prompt)
    
    def _apply_business_reasoning(self, query: str, context: Dict) -> str:
        """
        Apply business strategy reasoning
        
        Args:
            query: Business strategy question
            context: Session context
            
        Returns:
            Step-by-step business analysis
        """
        reasoning_prompt = f"""You are a CA assistant providing step-by-step business analysis.

Query: {query}
Context: {self._format_context(context)}

Please provide a detailed business analysis following this structure:

1. **Business Objective**: What is the client trying to achieve?
2. **Current Situation Analysis**: Assessment of the present scenario
3. **Available Options**: Different approaches or strategies available
4. **Pros and Cons Analysis**: Advantages and disadvantages of each option
5. **Financial Implications**: Cost-benefit analysis and financial impact
6. **Risk Assessment**: Business and regulatory risks
7. **Recommended Strategy**: Best approach with implementation steps

Provide clear reasoning for each recommendation."""
        
        return self._generate_reasoning_response(reasoning_prompt)
    
    def _apply_compliance_reasoning(self, query: str, context: Dict) -> str:
        """
        Apply compliance analysis reasoning
        
        Args:
            query: Compliance question
            context: Session context
            
        Returns:
            Step-by-step compliance analysis
        """
        reasoning_prompt = f"""You are a CA assistant providing step-by-step compliance analysis.

Query: {query}
Context: {self._format_context(context)}

Please provide a detailed compliance analysis following this structure:

1. **Compliance Requirement**: What specific compliance is needed?
2. **Applicable Regulations**: Which laws/rules mandate this compliance?
3. **Current Status Assessment**: Where does the client stand currently?
4. **Gap Analysis**: What needs to be done to achieve compliance?
5. **Step-by-Step Action Plan**: Detailed implementation steps
6. **Timeline and Deadlines**: When each step should be completed
7. **Documentation Required**: What records/documents are needed
8. **Consequences of Non-Compliance**: Penalties or risks if not complied

Provide a clear roadmap to achieve full compliance."""
        
        return self._generate_reasoning_response(reasoning_prompt)
    
    def _apply_general_reasoning(self, query: str, context: Dict) -> str:
        """
        Apply general reasoning for complex queries
        
        Args:
            query: General complex question
            context: Session context
            
        Returns:
            Step-by-step general analysis
        """
        reasoning_prompt = f"""You are a CA assistant providing step-by-step analysis.

Query: {query}
Context: {self._format_context(context)}

Please think through this step by step:

1. **Understanding the Question**: What is the client asking?
2. **Information Gathering**: What information do I need to answer this?
3. **Analysis Framework**: How should I approach this problem?
4. **Key Considerations**: What are the important factors to consider?
5. **Professional Assessment**: What is my professional opinion?
6. **Recommendations**: What should the client do?
7. **Next Steps**: What follow-up actions are needed?

Provide detailed reasoning for each step with professional CA guidance."""
        
        return self._generate_reasoning_response(reasoning_prompt)
    
    def _generate_reasoning_response(self, prompt: str) -> str:
        """
        Generate reasoning response using OpenAI
        
        Args:
            prompt: Reasoning prompt
            
        Returns:
            Generated reasoning response
        """
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant providing detailed step-by-step reasoning. Always structure your responses clearly and provide thorough analysis."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,  # Lower temperature for more consistent reasoning
                max_tokens=1500
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"Error generating reasoning response: {str(e)}")
            return "I encountered an error while processing your complex query. Please try again or break down your question into smaller parts."
    
    def _format_context(self, context: Dict) -> str:
        """
        Format conversation context for reasoning
        
        Args:
            context: Session context
            
        Returns:
            Formatted context string
        """
        if not context.get('context'):
            return "No previous context available"
        
        recent_context = context['context'][-2:]  # Last 2 exchanges for reasoning
        formatted_context = ""
        
        for exchange in recent_context:
            formatted_context += f"Previous Q: {exchange['query']}\n"
            formatted_context += f"Previous A: {exchange['response'][:300]}...\n\n"
        
        return formatted_context if formatted_context else "No previous context available"
    
    def _fallback_reasoning(self, query: str, context: Dict) -> str:
        """
        Fallback reasoning when main reasoning fails
        
        Args:
            query: User's question
            context: Session context
            
        Returns:
            Basic reasoning response
        """
        return f"""I understand you have a complex question: "{query}"

While I encountered a technical issue with my detailed reasoning process, let me provide a basic analysis:

**Key Points to Consider:**
• This appears to be a complex matter requiring careful analysis
• Multiple factors may need to be evaluated
• Professional judgment and expertise are important
• Specific circumstances of your case matter

**Recommended Approach:**
1. Break down the question into smaller, specific parts
2. Consider all relevant regulations and requirements
3. Evaluate the financial and legal implications
4. Seek additional clarification if needed

Would you like to rephrase your question or break it down into more specific parts? I can then provide more detailed guidance on each aspect."""


if __name__ == "__main__":
    # Simple test
    from openai import OpenAI
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    reasoning_engine = ChainOfThoughtReasoning(openai_client)
    print("Reasoning Engine initialized successfully!")
    
    # Test reasoning classification
    test_queries = [
        "How do I calculate my tax liability?",
        "What are the legal requirements for partnership registration?",
        "Should I incorporate my business as a company or LLP?"
    ]
    
    for query in test_queries:
        reasoning_type = reasoning_engine._classify_reasoning_type(query)
        print(f"Query: {query} -> Reasoning Type: {reasoning_type}")
