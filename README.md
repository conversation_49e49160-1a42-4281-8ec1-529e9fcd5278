# Multimodal Agentic RAG Chatbot

A sophisticated CA (Chartered Accountant) assistant chatbot with **RAG-first document intelligence** that prioritizes domain knowledge retrieval for document-related queries with intelligent fallback to normal LLM when documents are irrelevant.

## 🎯 Key Features

### RAG Prioritization Strategy
- **🥇 Tier 1: RAG Priority** (High relevance > 0.7) - Domain knowledge leads with document context
- **🥈 Tier 2: Hybrid Approach** (Medium relevance 0.3-0.7) - Combines RAG and document analysis  
- **🥉 Tier 3: LLM Fallback** (Low relevance < 0.3) - Direct LLM analysis for irrelevant documents

### Core Capabilities
- **Multimodal Document Processing**: PDF, DOCX, XLSX, Images (OCR), TXT files
- **Intelligent Query Routing**: Automatic routing based on document relevance and query type
- **Domain Expertise**: Specialized in CA/legal/tax/business domains via RAG
- **Real-time Information**: Web search for current data and trends
- **Chain of Thought Reasoning**: Complex problem-solving capabilities

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd multimodal-agentic-rag-chatbot

# Install dependencies
pip install -r requirements.txt

# Or use the setup script
python setup.py install
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your API keys
nano .env
```

Required configuration:
```env
OPENAI_API_KEY=your_openai_api_key_here
```

Optional configuration (enables enhanced features):
```env
QDRANT_URL=https://your-qdrant-cloud-url
QDRANT_API_KEY=your_qdrant_api_key_here
BRAVE_API_KEY=your_brave_search_api_key_here
```

### 3. Validation & Launch

```bash
# Validate setup
python setup.py validate

# Launch the chatbot
streamlit run app.py
```

## 📋 System Requirements

- **Python**: 3.9 or higher
- **Memory**: 512MB RAM per active session
- **Storage**: 100MB for application + temp files
- **Network**: Internet connection for API calls

## 🏗️ Architecture

### RAG Prioritization Flow
```
Document Upload → Relevance Assessment → Route Selection → Response Generation
     ↓                    ↓                    ↓              ↓
File Processing → Domain Scoring (0-1) → Tier Selection → Appropriate Response
```

### Three-Tier Processing Strategy

#### Tier 1: RAG Priority (Score > 0.7)
- **When**: Document contains significant CA/legal/tax/business content
- **Approach**: RAG knowledge base as PRIMARY source
- **Document Role**: Supplementary context
- **Example**: Tax invoice analysis with GST knowledge base

#### Tier 2: Hybrid Approach (Score 0.3-0.7)  
- **When**: Document has some CA relevance but limited domain content
- **Approach**: Combines RAG knowledge with direct document analysis
- **Balance**: Equal weight to knowledge base and document content
- **Example**: Business contract with some legal clauses

#### Tier 3: LLM-Only Fallback (Score < 0.3)
- **When**: Document is irrelevant to CA domain
- **Approach**: Direct LLM analysis with CA context awareness
- **Acknowledgment**: System notes document is outside primary expertise
- **Example**: Recipe document - provides analysis while noting limited CA relevance

## 📁 Project Structure

```
multimodal-agentic-rag-chatbot/
├── app.py                      # Streamlit web interface
├── chatbot.py                  # Main chatbot class
├── agent_router.py             # Query routing with RAG prioritization
├── rag_system.py              # RAG system with document context
├── document_analyzer.py        # Multi-format document processing
├── web_search_tool.py         # Web search integration
├── session_manager.py         # Session and context management
├── reasoning_engine.py        # Chain-of-thought reasoning
├── requirements.txt           # Python dependencies
├── setup.py                   # Setup and validation script
├── .env.example              # Environment configuration template
├── multimodal_agentic_rag_prd.md  # Product Requirements Document
└── README.md                 # This file
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Required | Description | Default |
|----------|----------|-------------|---------|
| `OPENAI_API_KEY` | ✅ | OpenAI API key for LLM | None |
| `QDRANT_URL` | ❌ | Qdrant vector database URL | Fallback mode |
| `QDRANT_API_KEY` | ❌ | Qdrant API key | Fallback mode |
| `BRAVE_API_KEY` | ❌ | Brave Search API key | Fallback mode |
| `MAX_SESSION_AGE` | ❌ | Session timeout (seconds) | 3600 |
| `MAX_FILE_SIZE` | ❌ | Max upload size (bytes) | 10MB |

### Fallback Modes

When optional services are unavailable:
- **RAG System**: Uses built-in CA knowledge base
- **Web Search**: Provides guidance to official sources
- **Vector Database**: Uses keyword-based retrieval

## 💡 Usage Examples

### Document-Related Queries (RAG Prioritized)
```
User: "What are the tax implications of this invoice?"
System: [Assesses document relevance → High → Uses RAG Priority]
Response: "Based on my knowledge base and your document: This GST invoice shows..."
```

### General CA Queries (Knowledge Base)
```
User: "What are the tax deductions available for AY 2024-25?"
System: [No document → Standard RAG query]
Response: "Based on current tax regulations, the following deductions are available..."
```

### Irrelevant Document Handling
```
User: "What is this recipe about?" [uploads cooking recipe]
System: [Assesses document relevance → Low → LLM Fallback]
Response: "This appears to be outside my primary expertise area, but I can see this is a cooking recipe..."
```

## 🧪 Testing

### Manual Testing
```bash
# Test individual components
python chatbot.py
python agent_router.py
python rag_system.py
python document_analyzer.py
```

### Setup Validation
```bash
python setup.py validate
```

## 🔍 Troubleshooting

### Common Issues

1. **"OpenAI API key not found"**
   - Ensure `.env` file exists with `OPENAI_API_KEY`
   - Check API key validity

2. **"Document processing failed"**
   - Verify file format is supported
   - Check file size (max 10MB)
   - Ensure temp directory exists

3. **"Qdrant connection failed"**
   - System will use fallback mode
   - Check `QDRANT_URL` and `QDRANT_API_KEY`

4. **"Web search unavailable"**
   - System will use fallback mode
   - Check `BRAVE_API_KEY` configuration

### Debug Mode
Set environment variable for detailed logging:
```bash
export PYTHONPATH=.
export LOG_LEVEL=DEBUG
streamlit run app.py
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the PRD document for detailed specifications
3. Create an issue in the repository

## 🔮 Roadmap

### Phase 1 (Current)
- ✅ RAG-first document analysis
- ✅ Multi-format document processing
- ✅ Intelligent query routing
- ✅ Streamlit web interface

### Phase 2 (Planned)
- 🔄 Advanced OCR capabilities
- 🔄 Voice input/output
- 🔄 Multi-language support
- 🔄 Enhanced reasoning engine

### Phase 3 (Future)
- 🔄 User authentication
- 🔄 Organization-specific knowledge bases
- 🔄 API access for integrations
- 🔄 Advanced analytics dashboard

---

**Built with ❤️ for CA professionals and businesses**
