"""
Document Analyzer with RAG Prioritization
Multi-format document processing with intelligent RAG-first approach
"""

import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Document processing imports
try:
    import PyPDF2
    from docx import Document
    import openpyxl
    from PIL import Image
    import pytesseract
except ImportError as e:
    print(f"Warning: Some document processing libraries not installed: {e}")

from openai import OpenAI

load_dotenv()

class DocumentAnalyzer:
    """
    Document analyzer implementing RAG-first approach with three-tier strategy
    """
    
    def __init__(self, rag_system):
        """
        Initialize document analyzer with RAG system reference
        
        Args:
            rag_system: RAGSystem instance for knowledge base queries
        """
        self.supported_formats = ['.pdf', '.docx', '.xlsx', '.jpg', '.png', '.txt']
        self.rag_system = rag_system
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.logger = logging.getLogger(__name__)
    
    def analyze_document(self, file_path: str) -> Dict[str, Any]:
        """
        Extract content from various document formats and assess relevance
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Document analysis data with content and relevance score
        """
        try:
            file_extension = os.path.splitext(file_path)[1].lower()
            
            if file_extension == '.pdf':
                doc_data = self.process_pdf(file_path)
            elif file_extension == '.docx':
                doc_data = self.process_docx(file_path)
            elif file_extension == '.xlsx':
                doc_data = self.process_excel(file_path)
            elif file_extension in ['.jpg', '.png']:
                doc_data = self.process_image(file_path)
            elif file_extension == '.txt':
                doc_data = self.process_text(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_extension}")
            
            # Assess document relevance to CA domain
            relevance_score = self.assess_document_relevance(doc_data['content'])
            doc_data['relevance'] = relevance_score
            
            self.logger.info(f"Document processed: {file_extension}, relevance: {relevance_score:.2f}")
            return doc_data
            
        except Exception as e:
            self.logger.error(f"Error analyzing document: {str(e)}")
            raise Exception(f"Failed to analyze document: {str(e)}")
    
    def assess_document_relevance(self, document_content: str) -> float:
        """
        Assess document relevance to CA domain (tax, legal, business)
        
        Args:
            document_content: Extracted text content from document
            
        Returns:
            Relevance score between 0-1
        """
        if not document_content or len(document_content.strip()) < 10:
            return 0.0
        
        # CA domain keywords with weights
        ca_keywords = {
            # Tax-related (high weight)
            'tax': 3, 'gst': 3, 'income tax': 4, 'tds': 3, 'tcs': 3, 'itr': 4,
            'deduction': 2, 'exemption': 2, 'section': 2, 'assessment': 3,
            
            # Legal/Compliance (high weight)
            'legal': 2, 'compliance': 3, 'audit': 3, 'company law': 4,
            'agreement': 2, 'contract': 2, 'mou': 2, 'deed': 2,
            
            # Business/Financial (medium weight)
            'business': 1, 'financial': 2, 'accounting': 3, 'bookkeeping': 2,
            'balance sheet': 4, 'profit loss': 4, 'invoice': 2, 'bill': 1,
            'receipt': 1, 'voucher': 2, 'ledger': 2, 'journal': 2,
            
            # Corporate (medium weight)
            'company': 1, 'corporation': 2, 'partnership': 2, 'proprietorship': 3,
            'registration': 2, 'license': 2, 'director': 1, 'shareholder': 1,
            
            # Professional terms (low weight)
            'ca': 2, 'chartered accountant': 4, 'professional': 1, 'advisory': 2
        }
        
        # Convert to lowercase for matching
        content_lower = document_content.lower()
        
        # Calculate weighted keyword score
        total_score = 0
        total_possible = sum(ca_keywords.values())
        
        for keyword, weight in ca_keywords.items():
            if keyword in content_lower:
                # Count occurrences (max 3 for any keyword to avoid skewing)
                occurrences = min(content_lower.count(keyword), 3)
                total_score += weight * occurrences
        
        # Normalize score to 0-1 range
        relevance_score = min(total_score / (total_possible * 0.5), 1.0)
        
        return relevance_score
    
    def answer_with_rag_priority(self, query: str, document_content: str, context: Dict) -> str:
        """
        Tier 1: Answer query primarily using RAG with document as additional context
        
        Args:
            query: User's question
            document_content: Document text content
            context: Session context
            
        Returns:
            RAG-prioritized response
        """
        try:
            # Use RAG system with document context
            rag_response = self.rag_system.query_with_document_context(
                query, document_content, context
            )
            
            # RAG response already includes source attribution
            if len(rag_response.strip()) > 50:
                return rag_response
            else:
                # Fallback to hybrid approach if RAG response is insufficient
                return self.answer_with_hybrid_approach(query, document_content, context)
                
        except Exception as e:
            self.logger.error(f"Error in RAG priority approach: {str(e)}")
            # Fallback to hybrid approach on error
            return self.answer_with_hybrid_approach(query, document_content, context)
    
    def answer_with_hybrid_approach(self, query: str, document_content: str, context: Dict) -> str:
        """
        Tier 2: Combine RAG knowledge with document analysis
        
        Args:
            query: User's question
            document_content: Document text content
            context: Session context
            
        Returns:
            Hybrid response combining RAG and document analysis
        """
        try:
            # Get RAG response
            try:
                rag_response = self.rag_system.query(query, context)
            except:
                rag_response = "No relevant information found in knowledge base."
            
            # Analyze document with LLM
            llm_response = self.analyze_document_with_llm(query, document_content)
            
            # Combine responses intelligently
            combined_prompt = f"""You are a CA assistant. Answer the user's query by combining information from:

1. Knowledge Base Response: {rag_response}
2. Document Analysis: {llm_response}

User Query: {query}

Provide a comprehensive answer that:
- Prioritizes knowledge base information when available
- Supplements with document-specific details
- Clearly indicates sources of information
- Maintains professional CA advisory tone
- Notes any discrepancies between sources"""
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant combining knowledge base and document analysis."},
                    {"role": "user", "content": combined_prompt}
                ],
                temperature=0.3,
                max_tokens=1200
            )

            generated_response = response.choices[0].message.content

            # Add source attribution for hybrid approach
            source_info = "🔄 **Source**: Hybrid (Knowledge Base + AI Analysis + 📄 Uploaded Document)"
            return f"{generated_response}\n\n{source_info}"
            
        except Exception as e:
            self.logger.error(f"Error in hybrid approach: {str(e)}")
            return self.answer_with_llm_only(query, document_content, context)
    
    def answer_with_llm_only(self, query: str, document_content: str, context: Dict) -> str:
        """
        Tier 3: Answer using LLM only when document is not relevant to CA domain
        
        Args:
            query: User's question
            document_content: Document text content
            context: Session context
            
        Returns:
            LLM-only response with CA context awareness
        """
        prompt = f"""You are a CA assistant. The user has uploaded a document that appears to be outside 
the typical CA domain (tax, legal, business). However, they have asked a question about it.

Document Content: {document_content[:2000]}...

User Query: {query}

Please answer their question about the document while noting that this appears to be 
outside your primary expertise area. If possible, relate it back to any CA-relevant 
aspects or provide general business context."""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant handling documents outside your primary domain."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            generated_response = response.choices[0].message.content

            # Add source attribution for LLM-only approach
            source_info = "🤖 **Source**: AI Analysis + 📄 Uploaded Document (Document outside CA domain)"
            return f"{generated_response}\n\n{source_info}"
            
        except Exception as e:
            self.logger.error(f"Error in LLM-only approach: {str(e)}")
            return "I'm experiencing technical difficulties analyzing this document. Please try again or contact support."

    def analyze_document_with_llm(self, query: str, document_content: str) -> str:
        """
        Analyze document content using LLM for hybrid approach

        Args:
            query: User's question
            document_content: Document text content

        Returns:
            LLM analysis of the document
        """
        prompt = f"""Analyze the following document content and answer the user's query:

Document Content: {document_content[:1500]}...

User Query: {query}

Provide a focused analysis based on the document content."""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a document analyst. Provide accurate analysis based on document content."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=800
            )

            return response.choices[0].message.content

        except Exception as e:
            self.logger.error(f"Error in LLM document analysis: {str(e)}")
            return "Unable to analyze document content."

    def analyze_query_without_document(self, query: str, context: Dict) -> str:
        """
        Handle document analysis queries when no document is uploaded

        Args:
            query: User's question
            context: Session context

        Returns:
            Response explaining need for document
        """
        return f"""I understand you want me to analyze or review something, but I don't see any uploaded document.

To help you with document analysis, please:
1. Upload your document (PDF, DOC, XLS, images, or TXT files)
2. Ask your question about the document

I can analyze various types of documents including:
• Tax documents (ITR, invoices, receipts)
• Legal documents (agreements, contracts)
• Financial statements (balance sheets, P&L)
• Business documents (registration papers, licenses)
• Images with text (using OCR)

Once you upload a document, I'll use my CA expertise to provide detailed analysis with relevant insights."""

    def process_pdf(self, file_path: str) -> Dict[str, Any]:
        """Extract text from PDF"""
        try:
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"

            return {
                'content': text.strip(),
                'type': 'pdf',
                'metadata': {'pages': len(reader.pages)}
            }
        except Exception as e:
            self.logger.error(f"Error processing PDF: {str(e)}")
            return {
                'content': f"Error processing PDF: {str(e)}",
                'type': 'pdf',
                'metadata': {'pages': 0}
            }

    def process_docx(self, file_path: str) -> Dict[str, Any]:
        """Extract text from DOCX"""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"

            return {
                'content': text.strip(),
                'type': 'docx',
                'metadata': {'paragraphs': len(doc.paragraphs)}
            }
        except Exception as e:
            self.logger.error(f"Error processing DOCX: {str(e)}")
            return {
                'content': f"Error processing DOCX: {str(e)}",
                'type': 'docx',
                'metadata': {'paragraphs': 0}
            }

    def process_excel(self, file_path: str) -> Dict[str, Any]:
        """Extract text from Excel"""
        try:
            workbook = openpyxl.load_workbook(file_path)
            text = ""

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text += f"Sheet: {sheet_name}\n"

                for row in sheet.iter_rows(values_only=True):
                    row_text = " | ".join([str(cell) if cell is not None else "" for cell in row])
                    if row_text.strip():
                        text += row_text + "\n"
                text += "\n"

            return {
                'content': text.strip(),
                'type': 'xlsx',
                'metadata': {'sheets': len(workbook.sheetnames)}
            }
        except Exception as e:
            self.logger.error(f"Error processing Excel: {str(e)}")
            return {
                'content': f"Error processing Excel: {str(e)}",
                'type': 'xlsx',
                'metadata': {'sheets': 0}
            }

    def process_image(self, file_path: str) -> Dict[str, Any]:
        """Extract text from image using OCR"""
        try:
            image = Image.open(file_path)
            text = pytesseract.image_to_string(image)

            return {
                'content': text.strip(),
                'type': 'image',
                'metadata': {'size': image.size}
            }
        except Exception as e:
            self.logger.error(f"Error processing image: {str(e)}")
            return {
                'content': f"Error processing image: {str(e)}",
                'type': 'image',
                'metadata': {'size': (0, 0)}
            }

    def process_text(self, file_path: str) -> Dict[str, Any]:
        """Extract text from TXT file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()

            return {
                'content': text.strip(),
                'type': 'txt',
                'metadata': {'length': len(text)}
            }
        except Exception as e:
            self.logger.error(f"Error processing text file: {str(e)}")
            return {
                'content': f"Error processing text file: {str(e)}",
                'type': 'txt',
                'metadata': {'length': 0}
            }


if __name__ == "__main__":
    # Simple test
    from rag_system import RAGSystem

    rag_system = RAGSystem()
    analyzer = DocumentAnalyzer(rag_system)
    print("Document Analyzer initialized successfully!")

    # Test relevance assessment
    test_content = "GST Invoice No: 123 Date: 01/04/2024 Tax Amount: 1000 CGST: 90 SGST: 90"
    relevance = analyzer.assess_document_relevance(test_content)
    print(f"Test relevance score: {relevance:.2f}")
