"""
Web Search Tool using Brave Search API
Provides real-time information for current events and data
"""

import os
import logging
import requests
from typing import Dict, List, Any
from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()

class WebSearchTool:
    """
    Web search tool for real-time information retrieval
    """
    
    def __init__(self):
        """Initialize web search tool with Brave API"""
        self.brave_api_key = os.getenv('BRAVE_API_KEY')
        self.base_url = "https://api.search.brave.com/res/v1/web/search"
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.logger = logging.getLogger(__name__)
        
        if not self.brave_api_key:
            self.logger.warning("Brave API key not found. Web search will use fallback mode.")
    
    def search(self, query: str, count: int = 5) -> str:
        """
        Perform web search and generate response
        
        Args:
            query: Search query
            count: Number of results to retrieve
            
        Returns:
            Generated response based on search results
        """
        try:
            if self.brave_api_key:
                return self._search_with_brave_api(query, count)
            else:
                return self._search_fallback_mode(query)
                
        except Exception as e:
            self.logger.error(f"Error in web search: {str(e)}")
            return self._search_fallback_mode(query)
    
    def _search_with_brave_api(self, query: str, count: int) -> str:
        """
        Perform search using Brave Search API
        
        Args:
            query: Search query
            count: Number of results
            
        Returns:
            Generated response from search results
        """
        headers = {
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip',
            'X-Subscription-Token': self.brave_api_key
        }
        
        params = {
            'q': query,
            'count': count,
            'offset': 0,
            'mkt': 'en-IN',  # India market
            'safesearch': 'moderate',
            'freshness': 'pw'  # Past week for fresher results
        }
        
        try:
            response = requests.get(self.base_url, headers=headers, params=params, timeout=10)
            response.raise_for_status()
            search_results = response.json()
            
            # Process search results and generate response
            return self.generate_web_response(query, search_results)
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Brave API request failed: {str(e)}")
            return self._search_fallback_mode(query)
        except Exception as e:
            self.logger.error(f"Error processing Brave API response: {str(e)}")
            return self._search_fallback_mode(query)
    
    def generate_web_response(self, query: str, search_results: Dict) -> str:
        """
        Generate response from search results using LLM
        
        Args:
            query: Original search query
            search_results: Results from Brave Search API
            
        Returns:
            Generated response based on search results
        """
        try:
            # Extract relevant information from search results
            web_results = search_results.get('web', {}).get('results', [])
            
            if not web_results:
                return f"I couldn't find current information about '{query}'. This might be a very recent topic or the search terms might need adjustment."
            
            # Format search results for LLM processing
            formatted_results = ""
            for i, result in enumerate(web_results[:5], 1):
                title = result.get('title', 'No title')
                snippet = result.get('description', 'No description')
                url = result.get('url', '')
                
                formatted_results += f"{i}. {title}\n{snippet}\nSource: {url}\n\n"
            
            # Generate response using LLM
            prompt = f"""You are a CA assistant providing current information based on web search results.

User Query: {query}

Search Results:
{formatted_results}

Please provide a comprehensive answer based on the search results. Focus on:
1. Current and accurate information
2. Relevance to CA/business/legal context where applicable
3. Clear source attribution
4. Professional advisory tone

If the information relates to tax, legal, or business matters, provide appropriate CA guidance."""
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant providing current information from web search results."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            generated_response = response.choices[0].message.content

            # Add source attribution for web search
            source_info = "🌐 **Source**: Web Search (Real-time Information via Brave Search)"
            return f"{generated_response}\n\n{source_info}"
            
        except Exception as e:
            self.logger.error(f"Error generating web response: {str(e)}")
            return f"I found some information about '{query}' but encountered an error processing it. Please try rephrasing your question."
    
    def _search_fallback_mode(self, query: str) -> str:
        """
        Fallback mode when Brave API is not available
        
        Args:
            query: Search query
            
        Returns:
            Fallback response explaining limitation
        """
        fallback_response = f"""I understand you're looking for current information about "{query}".

Unfortunately, I don't have access to real-time web search at the moment. For the most current information, I recommend:

1. **Official Government Websites**: For tax and legal updates
   - Income Tax Department: incometaxindia.gov.in
   - GST Portal: gst.gov.in
   - MCA Portal: mca.gov.in

2. **Professional Resources**: For CA-related updates
   - ICAI website: icai.org
   - Tax and legal news portals
   - Professional newsletters

3. **Financial News**: For business and market information
   - Economic Times, Business Standard
   - Financial news websites

If you have specific questions about tax, legal, or business matters that don't require real-time data, I'd be happy to help with my existing knowledge base."""

        # Add source attribution for fallback mode
        source_info = "ℹ️ **Source**: Web Search Fallback (Guidance to Official Sources)"
        return f"{fallback_response}\n\n{source_info}"
    
    def is_web_search_query(self, query: str) -> bool:
        """
        Determine if query requires web search
        
        Args:
            query: User's question
            
        Returns:
            True if query needs current/real-time information
        """
        web_indicators = [
            'current', 'latest', 'now', 'today', 'recent', 'new',
            'updated', 'trending', 'news', 'who is', 'what happened',
            'when did', 'breaking', 'this year', '2024', '2025',
            'best', 'top', 'popular', 'famous'
        ]
        
        query_lower = query.lower()
        return any(indicator in query_lower for indicator in web_indicators)


if __name__ == "__main__":
    # Simple test
    web_search = WebSearchTool()
    print("Web Search Tool initialized successfully!")
    
    # Test search
    if web_search.brave_api_key:
        result = web_search.search("latest GST rates India")
        print(f"Search result: {result[:200]}...")
    else:
        print("Brave API key not configured - using fallback mode")
