"""
Streamlit App for Multimodal Agentic RAG Chatbot
CA Assistant with RAG-first document analysis and intelligent fallback
"""

import streamlit as st
import uuid
import time
import os
from typing import Optional

# Import chatbot components
from chatbot import MultimodalAgenticRAGChatbot

# Page configuration
st.set_page_config(
    page_title="CA Assistant - RAG-First Chatbot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #1f77b4;
        margin-bottom: 2rem;
    }
    .feature-box {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .upload-section {
        border: 2px dashed #1f77b4;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        margin: 1rem 0;
    }
    .chat-message {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 0.5rem;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .assistant-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .stFileUploader > div > div > div > div {
        padding: 0.5rem;
    }
    .stFileUploader > div > div > div > div > button {
        background-color: #f0f2f6;
        border: 1px dashed #1f77b4;
        border-radius: 0.5rem;
        padding: 0.5rem;
        font-size: 1.2rem;
    }
    .stFileUploader > div > div > div > div > button:hover {
        background-color: #e3f2fd;
        border-color: #2196f3;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'chatbot' not in st.session_state:
        st.session_state.chatbot = MultimodalAgenticRAGChatbot()
    
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    
    if 'uploaded_document' not in st.session_state:
        st.session_state.uploaded_document = None
    
    if 'document_processed' not in st.session_state:
        st.session_state.document_processed = False

def display_header():
    """Display the main header and introduction"""
    st.markdown('<h1 class="main-header">🤖 CA Assistant - Your Intelligent Legal, Tax & Business Advisor</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    <div class="feature-box">
        <h3>🎯 RAG-First Intelligence</h3>
        <p>Upload documents directly in the chat interface and get specialized CA guidance. The system automatically uses your knowledge base for domain queries and provides real source tracking.</p>
    </div>
    """, unsafe_allow_html=True)

def display_sidebar():
    """Display sidebar with features and information"""
    with st.sidebar:
        st.header("🚀 Key Features")
        
        st.markdown("""
        **RAG Prioritization Strategy:**
        - 🥇 **Tier 1**: RAG Priority (High relevance > 0.7)
        - 🥈 **Tier 2**: Hybrid Approach (Medium relevance 0.3-0.7)
        - 🥉 **Tier 3**: LLM-Only Fallback (Low relevance < 0.3)

        **Vector Store Collections:**
        - 📚 TAX-RAG-1
        - 📚 tax_documents
        """)
        
        st.markdown("---")
        
        st.markdown("""
        **Supported Documents:**
        - 📄 PDF files
        - 📝 Word documents (DOCX)
        - 📊 Excel files (XLSX)
        - 🖼️ Images (JPG, PNG) with OCR
        - 📋 Text files (TXT)
        """)
        
        st.markdown("---")
        
        st.markdown("""
        **Expertise Areas:**
        - 💰 Tax Planning & Compliance
        - ⚖️ Legal & Regulatory Matters
        - 🏢 Business Registration & Structure
        - 📊 Financial Analysis
        - 🔍 Document Analysis

        **Source Attribution:**
        - 📚 Knowledge Base (RAG)
        - 🤖 AI Analysis (LLM)
        - 🌐 Web Search (Real-time)
        - 🔄 Hybrid (Combined)
        - 📄 Document Analysis
        """)

        st.markdown("---")

        # Document status
        st.markdown("**📄 Document Status:**")
        display_document_status()

        # Session information
        if st.session_state.get('messages'):
            st.markdown("---")
            st.markdown("**Session Info:**")
            st.write(f"Messages: {len(st.session_state.messages)}")
            st.write(f"Session ID: {st.session_state.session_id[:8]}...")

def display_document_status():
    """Display current document status in sidebar"""
    if st.session_state.document_processed and st.session_state.uploaded_document:
        st.success(f"📄 **Active Document:** {st.session_state.uploaded_document}")
        st.caption("You can ask questions about this document or upload a new one.")

        if st.button("🗑️ Remove Document", type="secondary", use_container_width=True):
            st.session_state.uploaded_document = None
            st.session_state.document_processed = False
            st.rerun()
    else:
        st.info("📎 **No document uploaded**")
        st.caption("Use the file upload button next to the chat input to upload documents.")

def display_chat_interface():
    """Display the main chat interface with integrated file upload"""
    st.subheader("💬 Chat Interface")

    # Display chat history
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # File upload section above chat input
    with st.container():
        st.markdown("##### 📎 Upload Document (Optional)")
        uploaded_file = st.file_uploader(
            "Choose a document to analyze",
            type=['pdf', 'docx', 'xlsx', 'jpg', 'png', 'txt'],
            help="Upload documents for specialized CA analysis",
            key="file_uploader"
        )

        # Show current document status if any
        if st.session_state.document_processed and st.session_state.uploaded_document:
            st.success(f"📄 **Active Document:** {st.session_state.uploaded_document}")
        elif uploaded_file is None:
            st.info("💡 **Tip:** Upload a document above to get specialized analysis, or just ask general CA questions below.")

    # Handle file upload
    if uploaded_file is not None:
        if st.session_state.uploaded_document != uploaded_file.name or not st.session_state.document_processed:
            with st.spinner("🔄 Processing document..."):
                try:
                    # Process document
                    doc_data = st.session_state.chatbot.process_document(
                        uploaded_file,
                        st.session_state.session_id
                    )

                    st.session_state.uploaded_document = uploaded_file.name
                    st.session_state.document_processed = True

                    # Show success message in chat
                    success_message = f"✅ Document '{uploaded_file.name}' processed successfully! You can now ask questions about it."
                    st.session_state.messages.append({"role": "assistant", "content": success_message})
                    st.rerun()

                except Exception as e:
                    error_message = f"❌ Error processing document: {str(e)}"
                    st.session_state.messages.append({"role": "assistant", "content": error_message})
                    st.session_state.document_processed = False
                    st.rerun()

    # Chat input (must be outside any containers)
    prompt = st.chat_input("💬 Ask about tax, legal, business matters, or upload a document above...")

    # Handle chat input
    if prompt:
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})

        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)

        # Generate and display assistant response
        with st.chat_message("assistant"):
            with st.spinner("🤔 Analyzing your query and determining the best approach..."):
                try:
                    # Get uploaded file if available
                    uploaded_file_for_query = None
                    if st.session_state.document_processed and st.session_state.uploaded_document:
                        # Create a mock file object for the processed document
                        # In a real implementation, you might want to store the file data
                        pass

                    # Process query with RAG prioritization
                    response = st.session_state.chatbot.process_query(
                        prompt,
                        session_id=st.session_state.session_id,
                        uploaded_file=uploaded_file_for_query
                    )

                    # Display response
                    st.markdown(response)

                    # Add debug information in expander
                    if "Knowledge Base (RAG)" in response:
                        with st.expander("🔍 Source Details (Debug Info)", expanded=False):
                            if "Collections Used:" in response:
                                collections_line = [line for line in response.split('\n') if 'Collections Used:' in line]
                                if collections_line:
                                    st.write("**Collections Searched:**", collections_line[0].split('Collections Used:')[1].strip())

                            if "Total Results:" in response:
                                results_line = [line for line in response.split('\n') if 'Total Results:' in line]
                                if results_line:
                                    st.write("**Search Results:**", results_line[0].split('Total Results:')[1].strip())

                            if "Best Match Score:" in response:
                                score_line = [line for line in response.split('\n') if 'Best Match Score:' in line]
                                if score_line:
                                    score = score_line[0].split('Best Match Score:')[1].strip()
                                    st.write("**Relevance Score:**", score)

                            st.info("✅ This response used actual vector search results from your knowledge base indexes!")

                    elif "LLM Knowledge" in response:
                        with st.expander("🔍 Source Details (Debug Info)", expanded=False):
                            st.write("**Source:** LLM Knowledge Only")
                            st.info("ℹ️ This response used the AI's built-in knowledge, not your knowledge base.")

                    elif "Web Search" in response:
                        with st.expander("🔍 Source Details (Debug Info)", expanded=False):
                            st.write("**Source:** Real-time Web Search")
                            st.info("🌐 This response used live web search results.")

                except Exception as e:
                    error_message = f"I encountered an error processing your query: {str(e)}"
                    st.error(error_message)
                    response = error_message

        # Add assistant response to chat history
        st.session_state.messages.append({"role": "assistant", "content": response})

def display_example_queries():
    """Display example queries for user guidance"""
    with st.expander("💡 Example Queries", expanded=False):
        st.markdown("""
        **Document-Related Queries (RAG Prioritized):**
        - "What are the tax implications of this invoice?"
        - "Analyze this contract for compliance issues"
        - "Calculate the GST from this bill"
        - "Summarize the key points in this document"
        
        **General CA Queries (Knowledge Base):**
        - "What are the tax deductions available for AY 2024-25?"
        - "How do I register a private limited company?"
        - "What are the GST filing requirements?"
        - "Explain the audit requirements for companies"
        
        **Current Information Queries (Web Search):**
        - "What are the latest GST rates?"
        - "Recent changes in income tax law"
        - "Current interest rates for home loans"
        """)

def main():
    """Main application function"""
    # Initialize session state
    initialize_session_state()

    # Display header
    display_header()

    # Display sidebar
    display_sidebar()

    # Side panel for utilities (only show if there are messages or examples needed)
    if st.session_state.get('messages') or True:  # Always show for now
        col1, col2 = st.columns([4, 1])

        with col2:
            # Example queries
            display_example_queries()

            # Clear chat button
            if st.button("🗑️ Clear Chat History", type="secondary", use_container_width=True):
                st.session_state.messages = []
                st.session_state.session_id = str(uuid.uuid4())
                st.session_state.uploaded_document = None
                st.session_state.document_processed = False
                st.rerun()

    # Chat interface (must be outside columns for st.chat_input to work)
    display_chat_interface()

    # Footer
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666;'>
        <p>🤖 CA Assistant with RAG-First Intelligence | Built with Streamlit & OpenAI</p>
        <p><small>Upload documents above the chat for seamless analysis.</small></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
