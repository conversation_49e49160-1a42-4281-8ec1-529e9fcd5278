"""
Test the specific user queries from the image
"""

import os
import sys
from dotenv import load_dotenv

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def test_user_queries():
    """Test the specific queries from the user's image"""
    print("🔍 Testing Specific User Queries")
    print("=" * 60)
    
    try:
        from chatbot import MultimodalAgenticRAGChatbot
        
        chatbot = MultimodalAgenticRAGChatbot()
        
        # The exact queries from the user's image
        queries = [
            "What procedural safeguards must an auditor verify regarding Video-based Customer Identification Process (V-CIP), and what does arise if such processes are poorly implemented?",
            "If the RBI underwrites an INR-denominated bond overseas and the bond defaults on it, how must the auditor assess the exposure and subsequent reporting requirements?"
        ]
        
        for i, query in enumerate(queries, 1):
            print(f"\n📋 Query {i}:")
            print(f"❓ {query}")
            print("-" * 60)
            
            try:
                response = chatbot.process_query(query)
                
                # Check if both collections are being used
                if "TAX-RAG-1" in response and "tax_documents" in response:
                    print("✅ Both collections used successfully!")
                elif "TAX-RAG-1" in response:
                    print("⚠️  Only TAX-RAG-1 used")
                elif "tax_documents" in response:
                    print("⚠️  Only tax_documents used")
                else:
                    print("❌ No collection sources found")
                
                # Check if meaningful content is returned
                if "knowledge base does not provide specific information" in response.lower():
                    print("⚠️  Generic response - may need more specific content in knowledge base")
                elif len(response.strip()) > 200:
                    print("✅ Detailed response generated")
                else:
                    print("⚠️  Short response")
                
                # Show source attribution
                lines = response.split('\n')
                source_lines = [line for line in lines if "**Source**:" in line]
                if source_lines:
                    print(f"📚 {source_lines[0]}")
                
                print(f"\n💬 Response:")
                print(response)
                print("\n" + "="*60)
                
            except Exception as e:
                print(f"❌ Query failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_user_queries()
