"""
Collection Content Inspector
Inspects the actual content stored in vector collections to understand data structure
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, Filter, FieldCondition, MatchValue
except ImportError:
    print("Error: qdrant-client not installed. Please install it with: pip install qdrant-client")
    sys.exit(1)

from openai import OpenAI

class CollectionContentInspector:
    """Inspect actual content in vector collections"""
    
    def __init__(self):
        """Initialize inspector"""
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.qdrant_client = None
        self.collection_names = ["TAX-RAG-1", "tax_documents"]
        
        # Initialize Qdrant client
        try:
            self.qdrant_client = QdrantClient(
                url=os.getenv('QDRANT_URL'),
                api_key=os.getenv('QDRANT_API_KEY')
            )
            logger.info("✅ Qdrant client initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Qdrant client: {str(e)}")
            sys.exit(1)
    
    def inspect_collections(self):
        """Inspect content in both collections"""
        print("🔍 COLLECTION CONTENT INSPECTION")
        print("=" * 70)
        
        for collection_name in self.collection_names:
            print(f"\n📋 Inspecting Collection: {collection_name}")
            print("-" * 50)
            
            self.inspect_collection_content(collection_name)
    
    def inspect_collection_content(self, collection_name: str):
        """Inspect content structure and sample data"""
        try:
            # Get collection info
            collection_info = self.qdrant_client.get_collection(collection_name)
            print(f"✅ Collection: {collection_info.points_count} points")
            
            # Get sample points to understand structure
            print(f"\n📊 Sampling points from {collection_name}...")
            
            # Use scroll to get sample points
            scroll_result = self.qdrant_client.scroll(
                collection_name=collection_name,
                limit=5,  # Get 5 sample points
                with_payload=True,
                with_vectors=False
            )
            
            points = scroll_result[0]  # scroll returns (points, next_page_offset)
            
            if not points:
                print("⚠️ No points found in collection")
                return
            
            print(f"📝 Found {len(points)} sample points")
            
            # Analyze payload structure
            payload_structures = {}
            content_samples = []
            
            for i, point in enumerate(points):
                print(f"\n🔍 Point {i+1} (ID: {point.id}):")
                
                if point.payload:
                    payload_keys = list(point.payload.keys())
                    print(f"   Payload keys: {payload_keys}")
                    
                    # Track payload structure
                    structure_key = str(sorted(payload_keys))
                    payload_structures[structure_key] = payload_structures.get(structure_key, 0) + 1
                    
                    # Look for content fields
                    content_fields = ['text', 'content', 'page_content', 'chunk', 'document']
                    found_content = False
                    
                    for field in content_fields:
                        if field in point.payload:
                            content = str(point.payload[field])
                            if content.strip():
                                print(f"   📄 {field}: {content[:200]}...")
                                content_samples.append({
                                    'field': field,
                                    'content': content[:500],
                                    'length': len(content)
                                })
                                found_content = True
                                break
                    
                    if not found_content:
                        # Show all payload content for debugging
                        print(f"   📄 Full payload: {point.payload}")
                else:
                    print("   ⚠️ No payload found")
            
            # Summary
            print(f"\n📊 Collection {collection_name} Summary:")
            print(f"   Total points: {collection_info.points_count}")
            print(f"   Payload structures found: {len(payload_structures)}")
            
            for structure, count in payload_structures.items():
                print(f"   - Structure {structure}: {count} points")
            
            if content_samples:
                print(f"   Content samples found: {len(content_samples)}")
                avg_length = sum(s['length'] for s in content_samples) / len(content_samples)
                print(f"   Average content length: {avg_length:.0f} characters")
                
                # Show content field distribution
                field_counts = {}
                for sample in content_samples:
                    field_counts[sample['field']] = field_counts.get(sample['field'], 0) + 1
                print(f"   Content fields: {field_counts}")
            else:
                print("   ⚠️ No recognizable content found")
                
        except Exception as e:
            print(f"❌ Failed to inspect collection {collection_name}: {str(e)}")
    
    def test_search_with_actual_content(self):
        """Test search using terms that might actually be in the collections"""
        print(f"\n🎯 TESTING SEARCH WITH BROAD TERMS")
        print("=" * 70)
        
        # Use very broad search terms that are likely to be in tax/audit documents
        broad_terms = [
            "tax",
            "audit",
            "compliance",
            "financial",
            "income",
            "deduction",
            "assessment",
            "return",
            "procedure",
            "verification"
        ]
        
        for term in broad_terms:
            print(f"\n🔍 Searching for: '{term}'")
            print("-" * 30)
            
            embedding = self.get_embedding(term)
            if not embedding:
                print("❌ Failed to generate embedding")
                continue
            
            # Test both collections
            for collection_name in self.collection_names:
                self.search_with_term(collection_name, term, embedding)
    
    def search_with_term(self, collection_name: str, term: str, embedding: List[float]):
        """Search collection with a specific term"""
        try:
            if collection_name == "TAX-RAG-1":
                # Use text-dense for TAX-RAG-1
                results = self.qdrant_client.search(
                    collection_name=collection_name,
                    query_vector=("text-dense", embedding),
                    limit=3,
                    with_payload=True,
                    score_threshold=0.0
                )
            else:
                # Use default for tax_documents
                results = self.qdrant_client.search(
                    collection_name=collection_name,
                    query_vector=embedding,
                    limit=3,
                    with_payload=True,
                    score_threshold=0.0
                )
            
            if results:
                print(f"✅ {collection_name}: {len(results)} results")
                best_result = results[0]
                print(f"   Best score: {best_result.score:.4f}")
                
                # Show content if available
                if best_result.payload:
                    content_fields = ['text', 'content', 'page_content', 'chunk', 'document']
                    found_content = False
                    
                    for field in content_fields:
                        if field in best_result.payload:
                            content = str(best_result.payload[field])
                            if content.strip():
                                print(f"   Content ({field}): {content[:150]}...")
                                found_content = True
                                break
                    
                    if not found_content:
                        # Show what's actually in the payload
                        payload_preview = str(best_result.payload)[:200]
                        print(f"   Payload: {payload_preview}...")
                else:
                    print("   ⚠️ No payload in result")
            else:
                print(f"⚠️ {collection_name}: No results")
                
        except Exception as e:
            print(f"❌ {collection_name} search failed: {str(e)}")
    
    def get_embedding(self, text: str) -> List[float]:
        """Generate embedding for text"""
        try:
            response = self.openai_client.embeddings.create(
                input=text,
                model="text-embedding-ada-002"
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            return []
    
    def search_by_metadata(self):
        """Search by metadata to understand what's indexed"""
        print(f"\n🔍 SEARCHING BY METADATA")
        print("=" * 70)
        
        for collection_name in self.collection_names:
            print(f"\n📋 Metadata search in {collection_name}:")
            
            try:
                # Get points with different metadata patterns
                scroll_result = self.qdrant_client.scroll(
                    collection_name=collection_name,
                    limit=10,
                    with_payload=True,
                    with_vectors=False
                )
                
                points = scroll_result[0]
                
                if points:
                    print(f"   Found {len(points)} points")
                    
                    # Analyze metadata patterns
                    metadata_patterns = {}
                    file_types = set()
                    
                    for point in points:
                        if point.payload:
                            # Look for file information
                            if 'file_path' in point.payload:
                                file_path = point.payload['file_path']
                                file_ext = file_path.split('.')[-1].lower() if '.' in file_path else 'unknown'
                                file_types.add(file_ext)
                                print(f"   📄 File: {file_path}")
                            
                            # Look for metadata patterns
                            if 'metadata' in point.payload:
                                metadata = point.payload['metadata']
                                if isinstance(metadata, dict):
                                    for key in metadata.keys():
                                        metadata_patterns[key] = metadata_patterns.get(key, 0) + 1
                    
                    if file_types:
                        print(f"   File types found: {sorted(file_types)}")
                    
                    if metadata_patterns:
                        print(f"   Metadata patterns: {metadata_patterns}")
                else:
                    print("   No points found")
                    
            except Exception as e:
                print(f"   ❌ Metadata search failed: {str(e)}")

def main():
    """Main inspection function"""
    print("🚀 COLLECTION CONTENT INSPECTOR")
    print("=" * 70)
    
    # Check environment variables
    required_vars = ['OPENAI_API_KEY', 'QDRANT_URL', 'QDRANT_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return
    
    inspector = CollectionContentInspector()
    
    # Inspect collection content
    inspector.inspect_collections()
    
    # Test search with broad terms
    inspector.test_search_with_actual_content()
    
    # Search by metadata
    inspector.search_by_metadata()
    
    print(f"\n📊 INSPECTION COMPLETE")
    print("=" * 70)
    print("This inspection helps understand:")
    print("1. What content structure exists in each collection")
    print("2. What fields contain the actual text content")
    print("3. Whether the collections contain relevant tax/audit information")
    print("4. What search terms might work better")

if __name__ == "__main__":
    main()
